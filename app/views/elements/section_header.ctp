<?php
if (empty($sectionData)) {
    $header = isset($sectionHeader) ? $sectionHeader : 'Bon Voyage';

    $theImage = isset($sectionImage) ? $sectionImage : '/img/site/placeholders/bon-voyage-offices.jpg';
} else {
    $header = $sectionData[$sectionModel]['name'];

    $theImage = $sectionData['MainImage'];
}

echo $this->element('modules/page_content_header', array(
    'header'   => $header
));
?>

<section class="page-content-body js-page-content-body page-content-body--destination">

<?php echo $this->element('modules/section_nav', array(
        'accommodationsContent' => $accommodationsContent,
        'activitiesContent' => $activitiesContent,
        'imagesContent' => $imagesContent,
        'itinerariesContent' => $itinerariesContent,
        'youtubeVideosContent' => $youtubeVideosContent,
    )) ?>

    <?php if (!empty($mapData) && (!isset($hideTheImage) || $hideTheImage !== true)): ?>
        <?php echo $this->element('modules/image_and_map', array(
            'modifier' => 'itinerary',
            'mapData' => $mapData,
            'hideTheImage' => isset($hideTheImage) ? $hideTheImage : false,
            'showMap' => isset($showMap) ? $showMap : false
        )); ?>
    <?php endif; ?>

    <div class="page-content-body__inner">
        <?php echo $this->element('sidebar'); ?>
    </div>


    <div class="page-content-body__inner">
        <div class="page-content-body__content js-related-content">

            <?php // see element/section_footer.ctp for footer ?>
