// Add this function to initialize the map
document.addEventListener('DOMContentLoaded', function() {
    // Check if map element exists
    const mapElement = document.getElementById('image-and-map__map');
    if (mapElement) {
        // Initialize map
        initializeMap();
        
        // Add event listener for the map toggle button
        const mapToggle = document.querySelector('.map-toggle');
        if (mapToggle) {
            mapToggle.addEventListener('click', function() {
                const mapWrapper = document.querySelector('.image-and-map__map-wrapper--fixed');
                if (mapWrapper) {
                    mapWrapper.classList.toggle('active');
                }
            });
        }
        
        // Add event listener for the close button
        const closeButton = document.querySelector('.image-and-map__hide');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                const mapWrapper = document.querySelector('.image-and-map__map-wrapper--fixed');
                if (mapWrapper) {
                    mapWrapper.classList.remove('active');
                }
            });
        }
    }
});

function initializeMap() {
    // Map initialization code using the GMapsKey
    // This will be called when the DOM is loaded
}
