@charset "UTF-8";
/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
 * CONTENTS............You're reading it!
 * STYLE GUIE..........Follow these
 * VARS................Colors, widths, heights, etc
 * VENDOR IMPORTS......Set our reset defaults
 * SPRITES SETUP.......Configure and import sprites
 * MISC................Other stuff
 * LOCAL IMPORTS.......User defined styles
 */
/*------------------------------------*\
   $STYLE GUIDE
\*------------------------------------*/
/*
.declaration-order {
  // @extends, @custom-mixins

  // Positioning

  // Box-model

  // Typography

  // Visual

  // Misc
}
*/
/*------------------------------------*\
    $VARS
\*------------------------------------*/
/*------------------------------------*\
    $VENDOR IMPORTS
\*------------------------------------*/
/*! normalize-scss | MIT/GPLv2 License | bit.ly/normalize-scss */
/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS and IE text size adjust after device orientation change,
 *    without disabling user zoom.
 */
@import url("//fonts.googleapis.com/css?family=Lato:400,700,400italic,300");
html {
  font-family: sans-serif;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/**
 * Remove default margin.
 */
body {
  margin: 0;
}

/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */
audio,
canvas,
progress,
video {
  display: inline-block;
  /* 1 */
  vertical-align: baseline;
  /* 2 */
}

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 */
[hidden] {
  display: none;
}

/**
 * Hide the `template` element in IE 8/9/10/11, Safari, and Firefox < 22.
 */
template {
  display: none;
}

/* Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * Improve readability of focused elements when they are also in an
 * active/hover state.
 */
a:active,
a:hover {
  outline: 0;
}

/* Text-level semantics
   ========================================================================== */
/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted;
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */
b,
strong {
  font-weight: bold;
}

/**
 * Address styling not present in Safari and Chrome.
 */
dfn {
  font-style: italic;
}

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */
h1 {
  font-size: 2em;
  /* Set 1 unit of vertical rhythm on the top and bottom margins. */
  margin: 0.75em 0;
}

/**
 * Address styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000;
}

/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove border when inside `a` element in IE 8/9/10.
 */
img {
  border: 0;
}

/**
 * Correct overflow not hidden in IE 9/10/11.
 */
svg:not(:root) {
  overflow: hidden;
}

/* Grouping content
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari.
 */
figure {
  margin: 1.5em 40px;
}

/**
 * Address differences between Firefox and other browsers.
 */
hr {
  box-sizing: content-box;
  height: 0;
}

/**
 * Contain overflow in all browsers.
 */
pre {
  overflow: auto;
}

/**
 * Address odd `em`-unit font size rendering in all browsers.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

/* Forms
   ========================================================================== */
/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */
/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 * 4. Address `font-family` inconsistency between `textarea` and other form in IE 7
 * 5. Improve appearance and consistency with IE 6/7.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  /* 1 */
  font: inherit;
  /* 2 */
  margin: 0;
  /* 3 */
}

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible;
}

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none;
}

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 * 4. Remove inner spacing in IE 7 without affecting normal text inputs.
 *    Known issue: inner spacing remains in IE 6.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
}

/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}

/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
input {
  line-height: normal;
}

/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 * 3. Remove excess padding in IE 7.
 *    Known issue: excess padding remains in IE 6.
 */
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome.
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  box-sizing: content-box;
  /* 2 */
  /**
   * Remove inner padding and search cancel button in Safari and Chrome on OS X.
   * Safari (but not Chrome) clips the cancel button when the search input has
   * padding (and `textfield` appearance).
   */
}
input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 * 3. Correct alignment displayed oddly in IE 6/7.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */
textarea {
  overflow: auto;
}

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold;
}

/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

/*! HTML5 Boilerplate v4.3.0 | MIT License | http://h5bp.com/ */
/*
 * What follows is the result of much research on cross-browser styling.
 * Credit left inline and big thanks to Nicolas Gallagher, Jonathan Neal,
 * Kroc Camen, and the H5BP dev community and team.
 */
/* ==========================================================================
   Base styles: opinionated defaults
   ========================================================================== */
/*
 * Remove text-shadow in selection highlight: h5bp.com/i
 * These selection rule sets have to be separate.
 * Customize the background color to match your design.
 */
::-moz-selection {
  background: #b3d4fc;
  text-shadow: none;
}

::selection {
  background: #b3d4fc;
  text-shadow: none;
}

/*
 * A better looking default horizontal rule
 */
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}

/*
 * Remove the gap between images, videos, audio and canvas and the bottom of
 * their containers: h5bp.com/i/440
 */
audio,
canvas,
img,
svg,
video {
  vertical-align: middle;
}

/*
 * Remove default fieldset styles.
 */
fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

/*
 * Allow only vertical resizing of textareas.
 */
textarea {
  resize: vertical;
}

/* ==========================================================================
   Browse Happy prompt
   ========================================================================== */
.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}

/* ==========================================================================
   Helper classes
   ========================================================================== */
/*
 * Hide from both screenreaders and browsers: h5bp.com/u
 */
.hidden {
  display: none !important;
  visibility: hidden;
}

/*
 * Hide only visually, but have it available for screenreaders: h5bp.com/v
 */
.visuallyhidden, .feefo-microdata, .primary-search input[type=submit] {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

/*
 * Extends the .visuallyhidden class to allow the element to be focusable
 * when navigated to via the keyboard: h5bp.com/p
 */
.visuallyhidden.focusable:active, .focusable.feefo-microdata:active, .primary-search input.focusable[type=submit]:active,
.visuallyhidden.focusable:focus,
.focusable.feefo-microdata:focus,
.primary-search input.focusable[type=submit]:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

/*
 * Hide visually and from screenreaders, but maintain layout
 */
.invisible {
  visibility: hidden;
}

/*
 * Clearfix: contain floats
 *
 * For modern browsers
 * 1. The space content is one way to avoid an Opera bug when the
 *    `contenteditable` attribute is included anywhere else in the document.
 *    Otherwise it causes space to appear at the top and bottom of elements
 *    that receive the `clearfix` class.
 * 2. The use of `table` rather than `block` is only necessary if using
 *    `:before` to contain the top-margins of child elements.
 */
.clearfix:before, .content-block:before, .strip-module--enews-and-quote .enews-module fieldset:before, .feefo-mobile:before, .hero__inner:before, .home-columns:before, .image-and-map:before, .page-content-header:before, .page-content-header__feefo .feefo:before, .related-module:before, .strip-module:before, .strip-module__inner:before, .strip-module__feefo-review:before, .testimonial-extract:before, .share-btns:before, .page-header__inner:before, .breadcrumb ul:before, .page-footer__inner:before, .page-footer__about:before, .page-footer__holidays:before, .thirdp-associations:before, .bv-community:before, .page-wrapper__contact-module:before, .contact-form .input:before, .press-release:before, .press-release__text:before, .page-content-body .page-content-body__inner:before, .destinations-nav .destinations-nav__inner:before, .destinations-nav ul:before, .page-content-body__content:before, .youtube-videos:before, .holidays-and-activities:before, .search-results:before, .whats-hot-index:before, .whats-hot-main-image:before, .whats-hot-view:before,
.clearfix:after,
.content-block:after,
.strip-module--enews-and-quote .enews-module fieldset:after,
.feefo-mobile:after,
.hero__inner:after,
.home-columns:after,
.image-and-map:after,
.page-content-header:after,
.page-content-header__feefo .feefo:after,
.related-module:after,
.strip-module:after,
.strip-module__inner:after,
.strip-module__feefo-review:after,
.testimonial-extract:after,
.share-btns:after,
.page-header__inner:after,
.breadcrumb ul:after,
.page-footer__inner:after,
.page-footer__about:after,
.page-footer__holidays:after,
.thirdp-associations:after,
.bv-community:after,
.page-wrapper__contact-module:after,
.contact-form .input:after,
.press-release:after,
.press-release__text:after,
.page-content-body .page-content-body__inner:after,
.destinations-nav .destinations-nav__inner:after,
.destinations-nav ul:after,
.page-content-body__content:after,
.youtube-videos:after,
.holidays-and-activities:after,
.search-results:after,
.whats-hot-index:after,
.whats-hot-main-image:after,
.whats-hot-view:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}

.clearfix:after, .content-block:after, .strip-module--enews-and-quote .enews-module fieldset:after, .feefo-mobile:after, .hero__inner:after, .home-columns:after, .image-and-map:after, .page-content-header:after, .page-content-header__feefo .feefo:after, .related-module:after, .strip-module:after, .strip-module__inner:after, .strip-module__feefo-review:after, .testimonial-extract:after, .share-btns:after, .page-header__inner:after, .breadcrumb ul:after, .page-footer__inner:after, .page-footer__about:after, .page-footer__holidays:after, .thirdp-associations:after, .bv-community:after, .page-wrapper__contact-module:after, .contact-form .input:after, .press-release:after, .press-release__text:after, .page-content-body .page-content-body__inner:after, .destinations-nav .destinations-nav__inner:after, .destinations-nav ul:after, .page-content-body__content:after, .youtube-videos:after, .holidays-and-activities:after, .search-results:after, .whats-hot-index:after, .whats-hot-main-image:after, .whats-hot-view:after {
  clear: both;
}

/* ==========================================================================
   EXAMPLE Media Queries for Responsive Design.
   These examples override the primary ('mobile first') styles.
   Modify as content requires.
   ========================================================================== */
@media only screen and (min-width: 35em) {
  /* Style adjustments for viewports that meet the condition */
}
@media print, (-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 120dpi) {
  /* Style adjustments for high resolution devices */
}
/* ==========================================================================
   Print styles.
   Inlined to avoid required HTTP connection: h5bp.com/r
   ========================================================================== */
@media print {
  * {
    background: transparent !important;
    color: #000 !important;
    /* Black prints faster: h5bp.com/s */
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  /*
   * Don't show links for images, or javascript/internal links
   */
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
    /* h5bp.com/t */
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  img {
    max-width: 100% !important;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}
/*------------------------------------*\
    $SPRITES SETUP
\*------------------------------------*/
.icns-sprite, .icns-arrow-black-right-18x18, .hero__text a::after, .icns-arrow-brown-right-11x16, .related-module li a::after, .icns-arrow-hero-carousel-left, .hero--carousel__nav--left, .icns-arrow-hero-carousel-right, .hero--carousel__nav--right, .icns-arrow-orange-down-14x14, .arrow-orange-down-14:after, .icns-arrow-orange-down-27x27, .custom-select::after, .custom-select--button::after, .icns-arrow-orange-right-11x16, .icns-arrow-orange-right-14x14, .arrow-orange-right-14:after, .icns-arrow-orange-right-18x18, .icns-arrow-orange-right-31x31, .icns-arrow-red-left-14x14, .arrow-left-red-14:before, .content-block__link.content-block__link--back a:before, .pagination__pages li:first-child a:before, .press-releases-back a:before, .whats-hot-view__back a:before, .icns-arrow-red-left-21x21, .image-viewer__nav--left, .icns-arrow-red-right-11x16, .icns-arrow-red-right-14x14, .arrow-right-red-14:after, .content-block__link a:after, .enews-module__form button:after, .feefo__all:after, .gallery-module__read-more a:after, .home-columns .home-column__more-link a:after, .quote-module__request:after, .pagination__pages li:last-child a:after, .related-module__link a:after,
.related-module__view-all a:after, .testimonial-extract__read-more a:after, .contact-form button:after, .faqs-index__questions li a:after, .ui-modal-close:after, .press-release__text__link a:after, .youtube-videos__link a:after, .whats-hot-index__hot.whats-hot-index__hot--read-more a:after, .whats-hot-index__read-more a:after, .sidebar-nav a:before, .icns-arrow-red-right-21x21, .home-columns li a:after, .image-viewer__nav--right, .primary-search.primary-search--page-right-sidebar button, .icns-arrow-red-right-31x31, .gallery-module__image__btn, .icns-arrow-red-up-14x14, .arrow-up-red-14:before, .faqs-index__answers__top a:before, .icns-arrow-white-right-13x13, .arrow-right-white-13:after, .hero--carousel__enews a:after, .page-content-body .image-and-map__map-wrapper--fixed .image-and-map__hide:after, .feefo-mobile .feefo__all:after, .hero--carousel__call-us a::after,.icns-arrow-white-right-18x18, .icns-arrow-white-right-21x21, .primary-search button, .icns-menu, .nav-toggle span, .icns-minus, .active .section-content__link-header:after, .icns-plus, .section-content__link-header:after, .icns-search, .search-toggle {
  background-image: url('/img/generated/sprites/icns-s551f95e481.png');
  background-repeat: no-repeat;
}

.icns-arrow-black-right-18x18, .hero__text a::after {
  background-position: -13px -32px;
  height: 18px;
  width: 18px;
}

.icns-arrow-brown-right-11x16, .related-module li a::after {
  background-position: -28px 0;
  height: 16px;
  width: 11px;
}

.icns-arrow-hero-carousel-left, .hero--carousel__nav--left {
  background-position: 0 -239px;
  height: 98px;
  width: 58px;
}

.icns-arrow-hero-carousel-right, .hero--carousel__nav--right {
  background-position: 0 -337px;
  height: 98px;
  width: 58px;
}

.icns-arrow-orange-down-14x14, .arrow-orange-down-14:after {
  background-position: 0 -16px;
  height: 14px;
  width: 14px;
}

.icns-arrow-orange-down-27x27, .arrow-orange-down-14:after, .custom-select::after, .custom-select--button::after {
  background-position: 0 -115px;
  height: 27px;
  width: 27px;
}

.icns-arrow-orange-right-11x16 {
  background-position: -39px 0;
  height: 16px;
  width: 11px;
}

.icns-arrow-orange-right-14x14, .arrow-orange-right-14:after {
  background-position: -14px -16px;
  height: 14px;
  width: 14px;
}

.icns-arrow-orange-right-18x18 {
  background-position: -25px -50px;
  height: 18px;
  width: 18px;
}

.icns-arrow-orange-right-31x31 {
  background-position: -27px -115px;
  height: 31px;
  width: 31px;
}

.icns-arrow-red-left-14x14, .arrow-left-red-14:before, .content-block__link.content-block__link--back a:before, .pagination__pages li:first-child a:before, .press-releases-back a:before, .whats-hot-view__back a:before {
  background-position: -28px -16px;
  height: 14px;
  width: 14px;
}

.icns-arrow-red-left-21x21, .image-viewer__nav--left {
  background-position: 0 -89px;
  height: 21px;
  width: 21px;
}

.icns-arrow-red-right-11x16 {
  background-position: -42px -16px;
  height: 16px;
  width: 11px;
}

.icns-arrow-red-right-14x14, .arrow-right-red-14:after, .content-block__link a:after, .enews-module__form button:after, .feefo__all:after, .gallery-module__read-more a:after, .home-columns .home-column__more-link a:after, .quote-module__request:after, .pagination__pages li:last-child a:after, .related-module__link a:after,
.related-module__view-all a:after, .testimonial-extract__read-more a:after, .contact-form button:after, .faqs-index__questions li a:after, .ui-modal-close:after, .press-release__text__link a:after, .youtube-videos__link a:after, .whats-hot-index__hot.whats-hot-index__hot--read-more a:after, .whats-hot-index__read-more a:after, .sidebar-nav a:before {
  background-position: 0 0;
  height: 14px;
  width: 14px;
}

.icns-arrow-red-right-21x21, .home-columns li a:after, .image-viewer__nav--right, .primary-search.primary-search--page-right-sidebar button {
  background-position: 0 -68px;
  height: 21px;
  width: 21px;
}

.icns-arrow-red-right-31x31, .gallery-module__image__btn {
  background-position: -21px -146px;
  height: 31px;
  width: 31px;
}

.icns-arrow-red-up-14x14, .arrow-up-red-14:before, .faqs-index__answers__top a:before {
  background-position: -14px 0;
  height: 14px;
  width: 14px;
}

.icns-arrow-white-right-13x13, .arrow-right-white-13:after, .hero--carousel__enews a:after, .page-content-body .image-and-map__map-wrapper--fixed .image-and-map__hide:after, .feefo-mobile .feefo__all:after, .hero--carousel__call-us a::after, .secondary-nav a:after {
  background-position: 0 -32px;
  height: 13px;
  width: 13px;
}

.icns-arrow-white-right-18x18 {
  background-position: 0 -177px;
  height: 18px;
  width: 18px;
}

.icns-arrow-white-right-21x21, .primary-search button {
  background-position: -21px -68px;
  height: 21px;
  width: 21px;
}

.icns-menu, .nav-toggle span {
  background-position: 0 -50px;
  height: 17px;
  width: 25px;
}
.icns-menu:hover, .nav-toggle span:hover, .icns-menu.menu-hover, .nav-toggle span.menu-hover {
  background-position: -31px -32px;
}

.icns-minus, .active .section-content__link-header:after {
  background-position: -18px -177px;
  height: 31px;
  width: 31px;
}

.icns-plus, .section-content__link-header:after {
  background-position: 0 -208px;
  height: 31px;
  width: 31px;
}

.icns-search, .search-toggle {
  background-position: -21px -89px;
  height: 26px;
  width: 21px;
}
.icns-search:hover, .search-toggle:hover, .icns-search.search-hover, .search-hover.search-toggle {
  /* background-position: 0 -146px; */
}

.logos-sprite, .logos-abta, .thirdp-associations__abta, .logos-atol, .thirdp-associations__atol, .logos-bv-mobile, .page-header__logo, .logos-bv-tablet, .logos-discover-america, .thirdp-associations__discover-america, .logos-facebook-48, .bv-community__facebook, .logos-googleplus-48, .logos-iata, .thirdp-associations__iata, .logos-instagram-48, .bv-community__instagram, .logos-linkedin-48, .bv-community__linkedin, .logos-twitter-48, .bv-community__twitter, .logos-wordpress-48, .bv-community__wordpress, .logos-youtube-48, .bv-community__youtube {
  background-image: url('/img/generated/sprites/logos-sda0533a7e2.png');
  background-repeat: no-repeat;
}

.logos-abta, .thirdp-associations__abta {
  background-position: 0 -109px;
  height: 33px;
  width: 74px;
}

.logos-atol, .thirdp-associations__atol {
  background-position: 0 0;
  height: 38px;
  width: 38px;
}

.logos-bv-mobile, .page-header__logo {
  background-position: -74px -109px;
  height: 34px;
  width: 187px;
}

.logos-bv-tablet {
  background-position: 0 -143px;
  height: 50px;
  width: 281px;
}

.logos-discover-america, .thirdp-associations__discover-america {
  background-position: -141px -48px;
  height: 61px;
  width: 110px;
}

.logos-facebook-48, .bv-community__facebook {
  background-position: -38px 0;
  height: 48px;
  width: 48px;
}

.logos-googleplus-48 {
  background-position: -86px 0;
  height: 48px;
  width: 48px;
}

.logos-iata, .thirdp-associations__iata {
  background-position: 0 -48px;
  height: 30px;
  width: 45px;
}

.logos-instagram-48, .bv-community__instagram {
  background-position: -134px 0;
  height: 48px;
  width: 48px;
}

.logos-linkedin-48, .bv-community__linkedin {
  background-position: -45px -48px;
  height: 48px;
  width: 48px;
}

.logos-twitter-48, .bv-community__twitter {
  background-position: -182px 0;
  height: 48px;
  width: 48px;
}

.logos-wordpress-48, .bv-community__wordpress {
  background-position: -230px 0;
  height: 48px;
  width: 48px;
}

.logos-youtube-48, .bv-community__youtube {
  background-position: -93px -48px;
  height: 48px;
  width: 48px;
}

/*------------------------------------*\
    $LOCAL IMPORTS
\*------------------------------------*/
/*------------------------------------*\
   $FUNCTIONS
\*------------------------------------*/
/*------------------------------------*\
   $MIXINS
\*------------------------------------*/
/*------------------------------------*\
    $TYPOGRAPHY
\*------------------------------------*/
html {
  font-size: 93.75%;
  line-height: 1.4666666667em;
}

html {
  font-family: 'Lato', sans-serif;
  color: #565044;
}

b,
strong {
  font-weight: 700;
}

em {
  font-style: italic;
}

a {
  color: #a80000;
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}

h1 {
  font-size: 2em;
  line-height: 1.1em;
  margin-top: 0.7333333333em;
  margin-bottom: 0.7333333333em;
  font-weight: 300;
}

h2 {
  font-size: 1.4em;
  line-height: 1.2571428571em;
  margin-top: 1.0476190476em;
  margin-bottom: 1.0476190476em;
  font-weight: 300;
}

h3 {
  font-size: 1.7333333333em;
  line-height: 1.2692307692em;
  margin-top: 0.8461538462em;
  margin-bottom: 0.8461538462em;
  font-weight: 300;
}

h4 {
  font-size: 1.4em;
  line-height: 1.5714285714em;
  margin-top: 1.0476190476em;
  margin-bottom: 1.0476190476em;
  font-weight: 300;
}

h5 {
  font-size: 1.2666666667em;
  line-height: 1.7368421053em;
  margin-top: 1.1578947368em;
  margin-bottom: 0.5789473684em;
  font-weight: 400;
}

/*--------------------------------------------*\
    $CLASSES
\*--------------------------------------------*/
.page-wrapper__content, .page-wrapper__content--center, .hero__inner, .home-columns, .page-content-header, .strip-module__inner, .page-header__inner, .breadcrumb__inner, .page-footer__inner, .page-content-body .page-content-body__inner, .destinations-nav .destinations-nav__inner, .holidays-and-activities, .search-results {
  max-width: 1280px;
}

.center-horizontal-block, .page-wrapper__content--center, .hero__inner, .home-columns, .page-content-header, .strip-module__inner, .page-header__inner, .breadcrumb__inner, .page-footer__inner, .page-content-body .page-content-body__inner, .destinations-nav .destinations-nav__inner, .holidays-and-activities, .search-results {
  margin: 0 auto;
}

.center-vertical, .gallery-module__image__btn, .home-columns li a:after, .related-module li a::after {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}

.ir, .gallery-module__image__btn, .hero--carousel__nav, .image-viewer__nav, .page-header__logo, .primary-search button, .thirdp-associations a, .bv-community a {
  overflow: hidden;
  text-indent: 100%;
  white-space: nowrap;
}

.reset-bm, .reset-btn, .enews-module__form button, .hero--carousel__nav, .image-viewer__nav, .nav-toggle, .search-toggle, .primary-search button, .contact-form button, .strip-module__feefo-review blockquote, .testimonial-extract blockquote, .primary-nav ul, .secondary-nav ul, .page-footer__copyright {
  margin: 0;
  padding: 0;
}

.reset-btn, .enews-module__form button, .hero--carousel__nav, .image-viewer__nav, .nav-toggle, .search-toggle, .primary-search button, .contact-form button {
  border: none;
  background-color: transparent;
  outline: none;
}

.reset-list, .home-columns ul, .image-viewer__wrapper__images ul, .pagination__pages ul, .related-module ul, .sidebar-nav ul, .primary-nav ul, .secondary-nav ul, .breadcrumb ul, .page-footer__about ul, .bv-community, .footer-nav ul, .faqs-index__questions, .destinations-nav ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.basic-image-wrapper, .content-block__image, .home-columns .home-column__image-wrapper, .image-and-map, .image-viewer__main, .related-module__image, .ui-modal, .press-release__image, .whats-hot-index__image, .whats-hot-main-image, .whats-hot-view__images > div {
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 0 2px rgba(97, 97, 97, 0.25);
  line-height: 0;
}
.basic-image-wrapper img, .content-block__image img, .home-columns .home-column__image-wrapper img, .image-and-map img, .image-viewer__main img, .related-module__image img, .ui-modal img, .press-release__image img, .whats-hot-index__image img, .whats-hot-main-image img, .whats-hot-view__images > div img {
  max-width: 100%;
  height: auto !important;
}

.video-embed > {
  max-width: 100%;
}

#flashMessage {
  color: #a80000;
  font-weight: bold;
}

/*--------------------------------------------*\
$ARROWS
\*--------------------------------------------*/
.arrow:after, .arrow-13:after, .arrow-right-13:after, .arrow-right-white-13:after, .hero--carousel__enews a:after, .page-content-body .image-and-map__map-wrapper--fixed .image-and-map__hide:after, .arrow-14:after, .arrow-right-14:after, .arrow-right-red-14:after, .content-block__link a:after, .enews-module__form button:after, .feefo__all:after, .gallery-module__read-more a:after, .home-columns .home-column__more-link a:after, .quote-module__request:after, .pagination__pages li:last-child a:after, .related-module__link a:after,
.related-module__view-all a:after, .testimonial-extract__read-more a:after, .contact-form button:after, .faqs-index__questions li a:after, .ui-modal-close:after, .press-release__text__link a:after, .youtube-videos__link a:after, .whats-hot-index__hot.whats-hot-index__hot--read-more a:after, .whats-hot-index__read-more a:after, .arrow-orange-right-14:after, .arrow-orange-down-14:after, .arrow-left-14:after, .arrow-left-red-14:after, .content-block__link.content-block__link--back a:after, .pagination__pages li:first-child a:after, .press-releases-back a:after, .whats-hot-view__back a:after, .arrow-up-red-14:after, .faqs-index__answers__top a:after, .arrow-27:after, .arrow-right-27:after, .sidebar-nav a:after, .arrow:before, .arrow-13:before, .arrow-right-13:before, .arrow-right-white-13:before, .hero--carousel__enews a:before, .page-content-body .image-and-map__map-wrapper--fixed .image-and-map__hide:before, .arrow-14:before, .arrow-right-14:before, .arrow-right-red-14:before, .content-block__link a:before, .enews-module__form button:before, .feefo__all:before, .gallery-module__read-more a:before, .home-columns .home-column__more-link a:before, .quote-module__request:before, .pagination__pages li:last-child a:before, .related-module__link a:before,
.related-module__view-all a:before, .testimonial-extract__read-more a:before, .contact-form button:before, .faqs-index__questions li a:before, .ui-modal-close:before, .press-release__text__link a:before, .youtube-videos__link a:before, .whats-hot-index__hot.whats-hot-index__hot--read-more a:before, .whats-hot-index__read-more a:before, .arrow-orange-right-14:before, .arrow-orange-down-14:before, .arrow-left-14:before, .arrow-left-red-14:before, .content-block__link.content-block__link--back a:before, .pagination__pages li:first-child a:before, .press-releases-back a:before, .whats-hot-view__back a:before, .arrow-up-red-14:before, .faqs-index__answers__top a:before, .arrow-27:before, .arrow-right-27:before, .sidebar-nav a:before {
  display: inline-block;
  content: "";
}

.arrow-13, .arrow-right-13, .arrow-right-white-13, .hero--carousel__enews a, .page-content-body .image-and-map__map-wrapper--fixed .image-and-map__hide {
  font-size: 0.8666666667em;
  line-height: 1.6923076923em;
}

.arrow-14, .arrow-right-14, .arrow-right-red-14, .content-block__link a, .enews-module__form button, .feefo__all, .gallery-module__read-more a, .home-columns .home-column__more-link a, .quote-module__request, .pagination__pages li:last-child a, .related-module__link a,
.related-module__view-all a, .testimonial-extract__read-more a, .contact-form button, .faqs-index__questions li a, .ui-modal-close, .press-release__text__link a, .youtube-videos__link a, .whats-hot-index__hot.whats-hot-index__hot--read-more a, .whats-hot-index__read-more a, .arrow-orange-right-14, .arrow-orange-down-14, .arrow-left-14, .arrow-left-red-14, .content-block__link.content-block__link--back a, .pagination__pages li:first-child a, .press-releases-back a, .whats-hot-view__back a, .arrow-up-red-14, .faqs-index__answers__top a {
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
}

.arrow-27, .arrow-right-27 {
  font-size: 1.8em;
  line-height: 1.6296296296em;
}

.arrow-red, .arrow-right-red-14, .content-block__link a, .enews-module__form button, .feefo__all, .gallery-module__read-more a, .home-columns .home-column__more-link a, .quote-module__request, .pagination__pages li:last-child a, .related-module__link a,
.related-module__view-all a, .testimonial-extract__read-more a, .contact-form button, .faqs-index__questions li a, .ui-modal-close, .press-release__text__link a, .youtube-videos__link a, .whats-hot-index__hot.whats-hot-index__hot--read-more a, .whats-hot-index__read-more a, .arrow-left-red-14, .content-block__link.content-block__link--back a, .pagination__pages li:first-child a, .press-releases-back a, .whats-hot-view__back a, .arrow-up-red-14, .faqs-index__answers__top a {
  color: #a80000;
}

.arrow-orange, .arrow-orange-right-14, .arrow-orange-down-14 {
  color: #be7000;
}

.arrow-white, .arrow-right-white-13, .hero--carousel__enews a, .page-content-body .image-and-map__map-wrapper--fixed .image-and-map__hide {
  color: #fff;
}

.arrow-right-14:after, .arrow-right-red-14:after, .content-block__link a:after, .enews-module__form button:after, .feefo__all:after, .gallery-module__read-more a:after, .home-columns .home-column__more-link a:after, .quote-module__request:after, .pagination__pages li:last-child a:after, .related-module__link a:after,
.related-module__view-all a:after, .testimonial-extract__read-more a:after, .contact-form button:after, .faqs-index__questions li a:after, .ui-modal-close:after, .press-release__text__link a:after, .youtube-videos__link a:after, .whats-hot-index__hot.whats-hot-index__hot--read-more a:after, .whats-hot-index__read-more a:after, .arrow-orange-right-14:after, .arrow-orange-down-14:after {
  position: relative;
  top: 2px;
  right: auto;
  bottom: auto;
  left: 7px;
  margin-right: 7px;
}

.arrow-right-13:after, .arrow-right-white-13:after, .hero--carousel__enews a:after, .page-content-body .image-and-map__map-wrapper--fixed .image-and-map__hide:after {
  position: relative;
  top: 2px;
  right: auto;
  bottom: auto;
  left: 10px;
  margin-right: 7px;
}

.arrow-right-27:after {
  position: relative;
  top: 2px;
  right: auto;
  bottom: auto;
  left: 7px;
  margin-right: 7px;
}

.arrow-left-14, .arrow-left-red-14, .content-block__link.content-block__link--back a, .pagination__pages li:first-child a, .press-releases-back a, .whats-hot-view__back a, .arrow-up-red-14, .faqs-index__answers__top a {
  margin-left: 7px;
}
.arrow-left-14:before, .arrow-left-red-14:before, .content-block__link.content-block__link--back a:before, .pagination__pages li:first-child a:before, .press-releases-back a:before, .whats-hot-view__back a:before, .arrow-up-red-14:before, .faqs-index__answers__top a:before {
  position: relative;
  top: 2px;
  right: 7px;
  bottom: auto;
  left: auto;
}

/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
* IMPORTS..............Modules big enough they deserve their own sass file...
* SHARE BUTTONS........Social sharing buttons
*/
/*------------------------------------*\
    $IMPORTS
\*------------------------------------*/
/*------------------------------------*\
    $CONTENT BLOCKS
\*------------------------------------*/
.content-block {
  margin: 30px 0;
}
.content-block a {
  color: #a80000;
}
.content-block ul {
  overflow: hidden;
}
.content-block li {
  margin-bottom: 0.7857142857em;
  color: #6c4000;
}
.content-block h2,
.content-block h3,
.content-block h4 {
  color: #6c4000;
}
.content-block h2 {
  font-size: 26px;
  line-height: 30px;
}
.content-block h3,
.content-block h4 {
  font-size: 26px;
  line-height: 30px;
}
.content-block table td {
  padding: 0 10px 0 0;
}

.content-block__text > p,
.content-block__text > h2,
.content-block__text > h3,
.content-block__text > h4 {
  margin-top: 0;
}

.content-block__video iframe {
  width: 100%;
  height: 285px;
}

.content-block__image {
  width: 100%;
}

.content-block__image img {
  width: 100%;
}

.content-block__image--right {
  float: right;
  margin: 0 0 10px 10px;
}

.content-block__image--left {
  float: left;
  margin: 0 10px 10px 0;
}

.content-block__link a {
  font-size: 1em;
  line-height: 1.5714285714em;
  text-transform: uppercase;
}

.content-block__link.content-block__link--back a::after {
  display: none;
}

.content-block__cols {
  display: table-row;
}

.content-block__cols__col {
  display: table-cell;
}

.content-block__more {
  position: relative;
  top: -30px;
  display: none;
  width: 100%;
  height: 60px;
  padding: 30px 0 0;
  border: none;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, #f3f3f5 65%);
  text-align: center;
  color: #a80000;
  line-height: 30px;
}
.content-block__more:focus {
  outline: 0;
}

/*
* Media Queries
*/
@media (min-width: 690px) {
  .content-block__image {
    max-width: 40%;
  }

  .content-block__image img {
    max-width: 100%;
  }
}
/*------------------------------------*\
    $ENEWS MODULE
\*------------------------------------*/
.enews-module h3 span {
  white-space: nowrap;
}

.enews-module__form input[type=text],
.enews-module__form input[type=email] {
  display: block;
  padding: 4px;
  margin-bottom: 5px;
  border: solid 1px #a5a196;
}
.enews-module__form input[type=text]:-ms-input-placeholder,
.enews-module__form input[type=email]:-ms-input-placeholder {
  color: #aaa;
}
.enews-module__form input[type=text]::placeholder,
.enews-module__form input[type=email]::placeholder {
  color: #aaa;
}
.enews-module__form button {
  float: right;
  clear: both;
  margin: 5px 0 0 0;
  text-transform: uppercase;
  font-weight: 700;
}

.enews-module__our-ideas {
  max-width: 400px;
}

/*------------------------------------*\
    $ENEWS AND QUOTE
\*------------------------------------*/
.strip-module--enews-and-quote .enews-module input[type=text],
.strip-module--enews-and-quote .enews-module input[type=email] {
  width: 100%;
}
.strip-module--enews-and-quote .enews-module button {
  margin-right: 5px;
}

.strip-module--enews-and-quote .quote-module {
  width: 250px;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .strip-module--enews-and-quote .enews-module {
    float: left;
    width: 410px;
  }
  .strip-module--enews-and-quote .enews-module fieldset {
    float: left;
  }
  .strip-module--enews-and-quote .enews-module input[type=text],
  .strip-module--enews-and-quote .enews-module input[type=email] {
    float: left;
    width: 200px;
    margin-right: 5px;
  }
  .strip-module--enews-and-quote .enews-module .enews-module__form button {
    margin-top: -20px;
  }

  .strip-module--enews-and-quote .quote-module {
    float: right;
  }
}
@media (min-width: 980px) {
  .strip-module--enews-and-quote .enews-module {
    width: auto;
  }
  .strip-module--enews-and-quote .enews-module .enews-module__form button {
    margin-top: 5px;
    clear: none;
  }
}
/*------------------------------------*\
   $FEEFO
\*------------------------------------*/
.feefo__all {
  display: block;
  margin-top: 10px;
  text-transform: uppercase;
  line-height: 0.7333333333em;
}

.feefo-mobile {
  background-color: #85837f;
  padding: 10px 18px;
}
.feefo-mobile .feefo_score {
  float: left;
  margin-right: 20px;
}
.feefo-mobile .feefo__all {
  float: left;
  max-width: 90px;
  color: #fff;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .feefo-mobile {
    display: none;
  }
}
/*------------------------------------*\
    $HERO
\*------------------------------------*/
.hero {
  position: relative;
  overflow: hidden;
}
.hero footer {
  position: absolute;
  top: auto;
  right: 0px;
  bottom: 0px;
  left: 0px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.45);
  text-transform: uppercase;
}
.hero footer,
.hero footer a {
  color: #fff;
}

.hero__images {
  position: relative;
  height: 100%;
}
.hero__images img {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  margin-top: -123px;
  margin-left: -800px;
}
.csstransforms .hero__images img {
  margin-top: 0;
  margin-left: 0;
  transform: translate(-50%, -50%);
}

.hero__inner {
  width: 100%;
  padding: 20px 18px;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .hero__inner {
    padding: 10px 18px 20px;
  }
}
@media (min-width: 980px) {
  .hero__inner {
    display: table;
    padding: 10px;
  }
  .hero__inner > div {
    display: table-cell;
    vertical-align: top;
  }
}
/*------------------------------------*\
    $HERO (ERROR)
\*------------------------------------*/
.hero--error {
  height: 660px;
}
.hero--error .hero__inner {
  padding-top: 25px;
  font-size: 2.6666666667em;
  line-height: 1.1em;
  font-weight: 300;
}
.hero--error .hero__inner p {
  font-size: 0.65em;
  line-height: 1.2692307692em;
  text-transform: none;
}
.hero--error .hero__images img {
  top: 0;
  margin-top: 0;
}
.csstransforms .hero--error .hero__images img {
  transform: translate(-50%, 0);
}

/*------------------------------------*\
   $GALLERY MODULES
\*------------------------------------*/
.gallery-module {
  margin: 30px 0;
  border-width: 1px 0 3px;
  border-style: solid;
  padding-top: 0.3492063492em;
  padding-bottom: 0.3492063492em;
  border-color: #6c4000;
}
.gallery-module:first-child {
  margin-top: 0;
}
.gallery-module h3 {
  margin-top: 0.5238095238em;
  margin-bottom: 0.5238095238em;
  font-size: 1.4em;
  line-height: 1.1523809524em;
  color: #6c4000;
}

.gallery-module__image {
  position: relative;
  width: 216px;
  height: 140px;
  padding: 14px 11px 14px 13px;
  margin-left: -5px;
  overflow: hidden;
  background: url('/img/site/layout/modules/gallery/gallery-bg.png?1711536199') 0 0 no-repeat;
  line-height: 0;
}

.gallery-module__read-more {
  margin-top: 0.7333333333em;
  margin-bottom: 0.7333333333em;
}
.gallery-module__read-more a {
  font-size: 1em;
  line-height: 1.4666666667em;
}

/*------------------------------------*\
    $HERO CAROUSEL
\*------------------------------------*/
.hero--carousel {
  max-height: 660px;
}
.hero--carousel .hero__images {
  position: relative;
  height: 0;
  max-height: 660px;
  min-height: 300px;
  padding-bottom: 41.25%;
}
.hero--carousel .hero__images img {
  height: 100%;
}

.hero__image {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50% 50%;
}

.hero__text {
  position: relative;
  max-width: 1240px;
  margin: 30% auto 0;
  display: none;
}
.hero__text a {
  display: block;
  font-size: 1.6em;
  line-height: 1.375em;
  color: #444;
  text-decoration: none;
}
.hero__text a::after {
  position: relative;
  top: 2px;
  right: auto;
  bottom: auto;
  left: 10px;
  display: inline-block;
  content: "";
}

.hero--carousel__nav {
  display: none;
  z-index: 10;
  transition: opacity 0.5s ease-out;
}
.hero--carousel__nav:hover {
  opacity: .5;
}

.hero--carousel__nav--left {
  position: absolute;
  top: 50%;
  right: auto;
  bottom: auto;
  left: 60px;
  margin-top: -49px;
}

.hero--carousel__nav--right {
  position: absolute;
  top: 50%;
  right: 60px;
  bottom: auto;
  left: auto;
  margin-top: -49px;
}

.hero--carousel__enews {
  display: none;
  padding: 10px 0 0 0;
}

.hero--carousel__call-us a::after {
  position: relative;
  top: 2px;
  right: auto;
  bottom: auto;
  left: 10px;
  display: inline-block;
  content: "";
}

.hero--carousel__call-us__tel {
  display: block;
  font-size: 2.2666666667em;
  line-height: 1.2941176471em;
}
.hero--carousel__call-us__tel a::after {
  display: none;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .hero--carousel .hero__images {
    min-height: 410px;
  }

  .hero--carousel__nav {
    display: block;
  }

  .hero--carousel__call-us {
    position: absolute;
    left: 18px;
    bottom: 20px;
  }

  .hero--carousel__enews {
    display: block;
    float: right;
    padding: 0 0 0 20px;
    margin-top: 10px;
    width: 60%;
  }
}
@media (min-width: 980px) {
  .hero--carousel__call-us {
    border-left: none;
    border-right: 1px solid #565044;
  }

  .hero--carousel__enews {
    float: left;
    padding: 0 20px;
    margin-top: 0;
    margin-left: 0;
    width: 70%;
  }

  .hero--carousel__call-us {
    position: relative;
    left: auto;
    bottom: auto;
    width: 310px;
  }
}
@media (min-width: 1280px) {
  .hero--carousel__call-us {
    width: 360px;
  }

  .hero__text {
    display: block;
  }
}
@media (min-width: 1600px) {
  .hero__text {
    margin-top: 0;
    position: absolute;
    top: 500px;
    left: 50%;
    margin-left: -630px;
  }
}
/*------------------------------------*\
    $HERO BANNER
\*------------------------------------*/
.hero--banner {
  max-height: 246px;
}
.hero--banner .hero__image {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  height: 0;
  min-height: 80px;
  padding-top: 16%;
}

.hero--banner__inner {
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 2.6666666667em;
  line-height: 1.1em;
  font-weight: 300;
}

/*
* Media Queries
*/
/*------------------------------------*\
    $HOME COLUMNS
\*------------------------------------*/
.home-columns {
  padding: 0 18px;
}
.home-columns .home-column {
  padding: 10px 0;
}
.home-columns .home-column__image-wrapper img {
  width: 100%;
}
.home-columns .home-column__more-link a {
  text-transform: uppercase;
}
.home-columns li {
  padding-top: 1.1em;
  padding-bottom: 1.1em;
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
}
.home-columns li a {
  font-size: 1.0714285714em;
  line-height: 1.4666666667em;
}
.home-columns li a:not(:first-child) {
  display: inline;
  padding-right: 0;
}
.home-columns li a:not(:first-child):after {
  display: none;
}
.home-columns li:first-child a:first-child {
  font-size: 18px;
  font-weight: 700;
}
.home-columns li a {
  position: relative;
  display: block;
  padding-right: 22px;
  color: #a80000;
}
.home-columns li a:after {
  left: auto;
  content: "";
}

.home-column > h2 {
  border-width: 1px 0 3px;
  border-style: solid;
  padding-top: 0.3492063492em;
  padding-bottom: 0.3492063492em;
  border-color: #a80000;
  color: #a80000;
}
.home-column > h2 a:hover {
  text-decoration: none;
}

.home-column--featured {
  margin-top: 22px;
}
.home-column--featured iframe {
  width: 100%;
  height: 247px;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .home-column {
    float: left;
    width: 50%;
    width: calc(50% - 10px);
    margin-right: 20px;
  }

  .home-column:nth-child(even) {
    margin-right: 0;
  }

  .home-column--featured {
    clear: both;
    width: 100%;
    float: none;
  }

  .home-columns h2 br {
    display: block;
  }
}
@media (min-width: 980px) {
  .home-columns {
    padding: 0 10px;
  }
  .home-columns h2 br {
    display: block;
  }

  .home-column {
    width: calc(27.5% - 10px);
    float: left;
    margin-right: 20px;
  }

  .home-column--hot {
    position: relative;
    left: 45%;
  }

  .home-column--featured {
    position: relative;
    left: calc(-27.5% + 10px);
    clear: none;
    width: calc(45% - 20px);
    margin-right: 0;
  }

  .home-column__text-wrapper {
    margin-left: 0;
  }
}
@media (min-width: 1280px) {
  .home-columns {
    padding: 0;
  }
}
.image-and-map__image {
  float: left;
}

.image-and-map__map-wrapper {
  position: relative;
  float: right;
}

.image-and-map__map-wrapper__map {
  height: 100%;
}
.image-and-map__map-wrapper__map div {
  line-height: 1.5em;
}
.image-and-map__map-wrapper__map img {
  max-width: none;
}

.image-and-map__instructions {
  position: absolute;
  top: auto;
  right: 12px;
  bottom: 10px;
  left: auto;
  width: 208px;
  padding: 0 12px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  background: rgba(168, 0, 0, 0.8);
  font-size: 0.8666666667em;
  line-height: 1.5230769231em;
  border: 1px solid #fff;
  border-radius: 5px;
  background-clip: padding-box;
  color: #fff;
  border: 3px solid #fff;
}

.image-and-map__instructions__close {
  width: 26px;
  height: 26px;
  display: block;
  border: 3px solid #fff;
  border-radius: 50%;
  background: rgba(168, 0, 0, 0.8);
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
  color: #fff;
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  position: absolute;
  top: -11px;
  right: -9px;
  bottom: auto;
  left: auto;
}
.image-and-map__instructions__close span {
  display: inline-block;
  vertical-align: middle;
  line-height: 14px;
}

/*------------------------------------*\
    $IMAGE VIEWER
\*------------------------------------*/
.image-viewer__main {
  margin-bottom: 10px;
}

.image-viewer__wrapper {
  position: relative;
  padding: 10px 33px;
  background-color: #fff;
}

.image-viewer__wrapper__images {
  overflow: hidden;
}
.image-viewer__wrapper__images ul {
  position: relative;
  transition: margin-left 0.75s ease-in-out;
}

.image-viewer__wrapper__images__image {
  float: left;
  width: 130px;
  height: 118px;
  margin-right: 10px;
  overflow: hidden;
  cursor: pointer;
}
.image-viewer__wrapper__images__image:nth-child(3) {
  border: 3px solid #a80000;
}
.image-viewer__wrapper__images__image:nth-child(3) img {
  margin: -3px 0 0 -3px;
}

.image-viewer__nav--left {
  position: absolute;
  top: auto;
  right: auto;
  bottom: 59px;
  left: 6px;
}

.image-viewer__nav--right {
  position: absolute;
  top: auto;
  right: 6px;
  bottom: 59px;
  left: auto;
}

/*
* Media Queries
*/
/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
* CONTENTS...........................You're reading it!
* VARS...............................Colors, widths, heights, etc
* MOBILESUBNAV.......................Default styles the mobile sub nav
*/
/*------------------------------------*\
    $VARS
\*------------------------------------*/
/*------------------------------------*\
    $MOBILESUBNAV
\*------------------------------------*/
.mobile-sub-nav {
  overflow: hidden;
  clear: both;
}
.mobile-sub-nav .custom-select, .mobile-sub-nav .custom-select--button {
  max-width: 400px;
}
.mobile-sub-nav .custom-select select, .mobile-sub-nav .custom-select--button select {
  font-size: 20px;
  font-weight: bold;
  padding: .5em 1.9em .4em .5em;
  line-height: 1.2;
}
.mobile-sub-nav .custom-select select span, .mobile-sub-nav .custom-select--button select span {
  display: none;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .mobile-sub-nav .custom-select, .mobile-sub-nav .custom-select--button {
    max-width: 400px;
  }
  .mobile-sub-nav .custom-select select, .mobile-sub-nav .custom-select--button select {
    font-size: 24px;
    font-weight: normal;
  }
}
@media (min-width: 980px) {
  .mobile-sub-nav {
    display: none;
  }
}
/*------------------------------------*\
    $QUOTE MODULE
\*------------------------------------*/
.quote-module__request {
  text-transform: uppercase;
}

.quote-module__fully_bonded {
  color: #565044;
  text-decoration: underline;
}

/*------------------------------------*\
    $PAGE CONTENT HEADER
\*------------------------------------*/
.page-content-header {
  position: relative;
  padding: 20px 18px;
}

.page-content-header__header {
  color: #a80000;
  text-transform: uppercase;
}

.page-content-header__share {
  margin-top: 1.4666666667em;
}

.page-content-header__feefo {
  clear: both;
  width: 280px;
  margin-top: 0.7333333333em;
}
.page-content-header__feefo .feefo__all {
  float: right;
  width: 84px;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .page-content-header__share {
    position: absolute;
    top: 0px;
    right: 18px;
    bottom: auto;
    left: auto;
  }

  .page-content-header__header {
    float: left;
    width: 480px;
    margin-top: 45px;
  }

  .page-content-header__feefo {
    clear: none;
    float: right;
    margin: 40px 0 0 0;
  }
}
@media (min-width: 980px) {
  .page-content-header__header {
    margin-top: 0.7333333333em;
    width: 550px;
    width: calc(100% - 470px);
    margin-right: 20px;
  }

  .page-content-header__share {
    position: relative;
    top: auto;
    right: auto;
    float: left;
  }

  .page-content-header__feefo {
    margin: 0;
    margin-top: 0.7333333333em;
  }
}
@media (min-width: 1280px) {
  .page-content-header {
    padding: 20px 0;
  }
}
/*------------------------------------*\
    $PAGINATION
\*------------------------------------*/
.pagination {
  margin: 40px 0;
  padding: 10px 0;
  clear: both;
  background: rgba(247, 245, 241, 0.9);
}

.pagination__pages {
  text-align: center;
}
.pagination__pages li {
  display: inline-block;
  margin: 0 10px;
  font-size: 1.7333333333em;
  line-height: 1.6923076923em;
  font-weight: 300;
  vertical-align: middle;
}
.pagination__pages li:first-child a, .pagination__pages li:last-child a {
  font-size: 0.5384615385em;
  line-height: 3.1428571429em;
  text-transform: uppercase;
  font-weight: 400;
}
.pagination__pages a {
  color: #a80000;
}

/*------------------------------------*\
    $VARS
\*------------------------------------*/
/*------------------------------------*\
    $RELATED MODULE
\*------------------------------------*/
.related-module h3 {
  font-size: 1.2666666667em;
  line-height: 1.3894736842em;
  color: #6c4000;
  border-color: #6c4000;
  border-width: 1px 0 3px;
  border-style: solid;
  padding-top: 0.3859649123em;
  padding-bottom: 0.3859649123em;
}
.related-module li {
  padding-top: 1.4666666667em;
  padding-bottom: 1.4666666667em;
  font-size: 1em;
  line-height: 1.4666666667em;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
}
.related-module li:first-child {
  padding-top: 0;
}
.related-module li a {
  position: relative;
  display: block;
  padding-right: 22px;
}
.related-module li a::after {
  left: auto;
  content: "";
}

/*
* Media Queries
*/
@media (min-width: 980px) {
  .related-module {
    width: calc(50% - 15px);
  }

  .related-module__image {
    float: left;
    margin-left: -10px;
  }
}
/*------------------------------------*\
    $HOLIDAYS & DESTINATIONS
\*------------------------------------*/
.related-module--hols-dest a {
  color: #6c4000;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .related-module--hols-dest {
    float: left;
    width: 210px;
  }
}
@media (min-width: 980px) {
  .related-module--hols-dest {
    width: calc(50% - 15px);
  }
  .related-module--hols-dest ul {
    float: right;
    width: calc(100% - 256px);
  }
}
/*------------------------------------*\
    $ACTIVITIES
\*------------------------------------*/
.related-module--activities h3 {
  color: #a80000;
  border-color: #a80000;
}

.related-module__activity {
  clear: both;
}

.related-module__image {
  float: left;
  margin-bottom: 20px;
}
.related-module__image + * {
  clear: left;
}

.related-module__info {
  margin-bottom: 0.7333333333em;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
}
.related-module__info h4 {
  margin: 0 0 10px;
  font-size: 1.0666666667em;
  line-height: 1.375em;
  font-weight: 700;
}
.related-module__info h4 + p {
  margin-top: 0;
}
.related-module__info p {
  margin-top: 0em;
  margin-bottom: 0.7333333333em;
}
.related-module__info a {
  color: #a80000;
}

.related-module__link a,
.related-module__view-all a {
  line-height: 0.7333333333em;
  text-transform: uppercase;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .related-module--activities {
    float: right;
    width: calc(100% - 240px);
  }
  .related-module--activities .related-module__image img {
    max-width: 95px;
  }

  .related-module--hols-dest .related-module__image + * {
    clear: left;
  }

  .related-module__image + * {
    clear: none;
  }

  .related-module__info,
  .related-module__view-all {
    margin-left: 130px;
  }
}
@media (min-width: 980px) {
  .related-module--activities {
    width: calc(50% - 15px);
  }
  .related-module--activities .related-module__image img {
    max-width: 180px;
  }

  .related-module--hols-dest .related-module__image + * {
    clear: none;
  }

  .related-module__info,
  .related-module__view-all {
    float: right;
    width: calc(100% - 210px);
    margin-left: 0;
  }
}
/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
* CONTENTS...........................You're reading it!
* VARS...............................Colors, widths, heights, etc
* ANIMATIONS.........................Define keyframe animations
* SECTION CONTENT....................Default styles for section content
*/
/*------------------------------------*\
    $VARS
\*------------------------------------*/
/*------------------------------------*\
    $ANIMATIONS
\*------------------------------------*/
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/*------------------------------------*\
    $SECTION CONTENT
\*------------------------------------*/
.section-content-wrapper {
  margin-bottom: 30px;
}

.section-content__link-header {
  font-size: 1.0666666667em;
  line-height: 1.375em;
  background-color: transparent;
  position: relative;
  display: block;
  padding: 15px 0;
  color: #565044;
  text-transform: uppercase;
  border-style: solid;
  border-width: 1px 0 0;
  border-color: #565044;
  cursor: pointer;
  width: 100%;
}
.section-content__link-header:after {
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -15px;
  display: block;
  content: '';
}
.section-content__link-header:focus {
  outline: none;
}
.section-content__link-header br {
  content: ' ';
}

.loading .section-content__link-header:after {
  transform-origin: 50% 50%;
  animation: spin 1s infinite linear;
}

.section-content:last-child .section-content__link-header {
  border-width: 1px 0 3px;
}

.section-content__inner {
  display: none;
}
.active .section-content__inner {
  display: block;
  max-height: 25000px;
}

.section-content--blog .section-content__inner {
  display: block;
  max-height: none;
}

.section-content__inner {
  max-height: 0;
  display: block;
  overflow: hidden;
  transition: 3s max-height ease;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .section-content {
    display: none;
  }
  .section-content.active {
    display: block;
  }

  .section-content.section-content--blog {
    display: block;
  }

  .section-content__link-header {
    display: none;
  }
}
@media (min-width: 980px) {
  .section-content-wrapper {
    margin-bottom: 0;
  }
}
/* Container used for styling the custom select, the buttom class below adds the bg gradient, corners, etc. */
.custom-select, .custom-select--button {
  position: relative;
  display: block;
  margin-top: 0.5em;
  padding: 0;
}

.custom-select--button {
  border: 1px solid #dedbd4;
  border-radius: .4em;
  box-shadow: 0 1px 0 1px rgba(0, 0, 0, 0.04);
  background: #f3f3f3;
  /* Old browsers */
}

/* This is the native select, we're making everything but the text invisible so we can see the button styles in the wrapper */
.custom-select select, .custom-select--button select {
  width: 100%;
  margin: 0;
  background: none;
  border: 1px solid transparent;
  outline: none;
  /* Prefixed box-sizing rules necessary for older browsers */
  box-sizing: border-box;
  /* Remove select styling */
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  /* Font size must the 16px or larger to prevent iOS page zoom on focus */
  font-size: 1em;
  /* General select styles: change as needed */
  font-family: helvetica, sans-serif;
  font-weight: bold;
  color: #a80000;
  padding: .6em 1.9em .5em .8em;
  line-height: 1.3;
}

/* Custom arrow sits on top of the select - could be an image, SVG, icon font, etc. or the arrow could just baked into the bg image on the select. Note this si a 2x image so it will look bad in browsers that don't support background-size. In production, you'd handle this resolution switch via media query but this is a demo. */
.custom-select::after, .custom-select--button::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 1em;
  margin-top: -14px;
  z-index: 2;
  /* This hack make the select behind the arrow clickable in some browsers */
  pointer-events: none;
}

/* Hover style */
.custom-select:hover, .custom-select--button:hover {
  border: 1px solid #dedbd4;
}

/* Focus style */
.custom-select select:focus, .custom-select--button select:focus {
  outline: none;
  box-shadow: 0 0 1px 3px #b4defa;
  background-color: transparent;
  color: #a80000;
  border: 1px solid #aaa;
}

/* Set options to normal weight */
.custom-select option, .custom-select--button option {
  font-weight: normal;
}

/* ------------------------------------  */
/* START OF UGLY BROWSER-SPECIFIC HACKS */
/* ----------------------------------  */
/* OPERA - Pre-Blink nix the custom arrow, go with a native select button to keep it simple. Targeted via this hack http://browserhacks.com/#hack-a3f166304aafed524566bc6814e1d5c7 */
x:-o-prefocus, .custom-select::after, .custom-select--button::after {
  display: none;
}

/* IE 10/11+ - This hides native dropdown button arrow so it will have the custom appearance, IE 9 and earlier get a native select - targeting media query hack via http://browserhacks.com/#hack-28f493d247a12ab654f6c3637f6978d5 - looking for better ways to achieve this targeting */
/* The second rule removes the odd blue bg color behind the text in the select button in IE 10/11 and sets the text color to match the focus style's - fix via http://stackoverflow.com/questions/17553300/change-ie-background-color-on-unopened-focused-select-box */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .custom-select select::-ms-expand, .custom-select--button select::-ms-expand {
    display: none;
  }

  .custom-select select:focus::-ms-value, .custom-select--button select:focus::-ms-value {
    background: transparent;
    color: #a80000;
  }
}
/* FIREFOX won't let us hide the native select arrow, so we have to make it wider than needed and clip it via overflow on the parent container. The percentage width is a fallback since FF 4+ supports calc() so we can just add a fixed amount of extra width to push the native arrow out of view. We're applying this hack across all FF versions because all the previous hacks were too fragile and complex. You might want to consider not using this hack and using the native select arrow in FF. Note this makes the menus wider than the select button because they display at the specified width and aren't clipped. Targeting hack via http://browserhacks.com/#hack-758bff81c5c32351b02e10480b5ed48e */
/* Show only the native arrow */
@-moz-document url-prefix() {
  .custom-select, .custom-select--button {
    overflow: hidden;
  }

  .custom-select select, .custom-select--button select {
    width: 120%;
    width: calc(100% + em);
  }
}
/* Firefox focus has odd artifacts around the text, this kills that. See https://developer.mozilla.org/en-US/docs/Web/CSS/:-moz-focusring */
.custom-select select:-moz-focusring, .custom-select--button select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
}

/* ------------------------------------  */
/*  END OF UGLY BROWSER-SPECIFIC HACKS  */
/* ------------------------------------  */
/*------------------------------------*\
    $SIDEBAR NAV
\*------------------------------------*/
.sidebar-nav li {
  margin-top: 0.7333333333em;
  margin-bottom: 0.7333333333em;
}
.sidebar-nav a {
  position: relative;
  display: block;
  margin-left: 20px;
  color: #565044;
}
.sidebar-nav a:before {
  position: absolute;
  top: 5px;
  right: auto;
  bottom: auto;
  left: -20px;
}
.sidebar-nav .selected > a {
  font-weight: 700;
}

.sidebar-nav.sidebar-nav--destinations {
  min-height: 455px;
}

.sidebar-nav__inner {
  padding: 20px 10px;
  background-color: #fff;
  border-style: dashed;
  border-color: #565044;
  border-width: 1px 0;
}

/*------------------------------------*\
    $STRIP MODULE
\*------------------------------------*/
.strip-module {
  margin: 0;
  background-image: url('/img/site/layout/chrome/noise-lines.png?1711536199');
  background-repeat: repeat;
  background-size: 60px 59px;
  border-top: 1px solid #fff;
  border-bottom: 1px solid #fff;
}
.strip-module h3 {
  margin-bottom: 0.4230769231em;
}
.strip-module h3 + p {
  margin-top: 0.7333333333em;
}

.page-content-body .strip-module {
  margin: 0 -15px;
}

.strip-module__inner {
  padding: 15px 18px;
}

.strip-module__feefo-review blockquote {
  position: relative;
  padding-right: 30px;
  float: left;
}
.strip-module__feefo-review blockquote::before {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  height: 20px;
  font-size: 80px;
  color: #a80000;
  line-height: 70px;
}
.strip-module__feefo-review blockquote::before {
  content: "”";
}

.strip-module__feefo-review__content {
  margin: 0 40px 10px 0;
  font-size: 1.0666666667em;
  line-height: 1.375em;
}

.strip-module__feefo-review__date {
  display: block;
  color: #a80000;
  margin-bottom: 10px;
}

.strip-module__feefo a {
  color: #a80000;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .strip-module,
  .page-content-body .strip-module {
    margin: 0;
  }

  .strip-module__left {
    float: left;
    width: 100%;
    margin-right: -260px;
  }

  .strip-module__feefo-review {
    margin-right: 260px;
  }

  .strip-module__feefo {
    float: right;
    width: 220px;
    margin-left: 0;
  }

  .strip-module__feefo-review__date {
    margin-bottom: 0;
  }
}
@media (min-width: 980px) {
  .strip-module__inner {
    padding: 15px 10px;
  }
}
/*------------------------------------*\
$TESTIMONIAL
\*------------------------------------*/
.testimonial-extract {
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px 0;
}
.testimonial-extract blockquote {
  position: relative;
}
.testimonial-extract blockquote::before, .testimonial-extract blockquote::after {
  display: none;
  position: absolute;
  top: 0;
  height: 40px;
  font-size: 100px;
  color: #a80000;
  line-height: 100px;
  text-align: right;
}
.testimonial-extract blockquote::before {
  left: 0;
  content: "“";
}
.testimonial-extract blockquote::after {
  right: 0;
  content: "”";
}

.testimonial-extract--strip {
  border-bottom: none;
  background-image: url('/img/site/layout/chrome/noise-lines.png?1711536199');
  background-repeat: repeat;
  background-size: 60px 59px;
  border: 1px solid #fff;
}
.testimonial-extract--strip blockquote::before,
.testimonial-extract--strip blockquote::after {
  height: 30px;
  font-size: 70px;
  line-height: 60px;
}

.testimonial-extract__inner {
  max-width: 1280px;
  margin: 0 auto;
}

.testimonial-extract__feefo {
  margin-bottom: 20px;
}

.testimonial-extract__content,
.testimonial-extract__content__author {
  color: #565044;
}

.testimonial-extract__content {
  font-size: 1.0666666667em;
  line-height: 1.375em;
}
.testimonial-extract__content p:first-child {
  margin-top: 5px;
}

.testimonial-extract--strip .testimonial-extract__content {
  font-size: 1.0666666667em;
  line-height: 1.375em;
}

.testimonial-extract__content__author {
  float: left;
  font-size: 0.7777777778em;
  line-height: 1.5714285714em;
  font-style: normal;
}

.testimonial-extract__read-more {
  clear: both;
}

.testimonial-extract__date {
  font-style: italic;
}

@media (min-width: 768px) {
  .testimonial-extract__feefo {
    float: left;
  }
}
@media (min-width: 1280px) {
  .testimonial-extract__inner {
    padding: 0 10px;
  }
}
/*------------------------------------*\
    $TILE
\*------------------------------------*/
.tile-panel {
  margin-bottom: 20px;
  overflow: hidden;
  display: block;
  position: relative;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}
.tile-panel:before {
  content: '';
  float: left;
  height: 0;
  padding-top: 75%;
}

.tile-panel__content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
}

.csstransitions .tile-panel__content {
  transition: 0.3s;
}

.tile-panel:hover .tile-panel__content {
  bottom: 0 !important;
}

.tile-panel__content a {
  color: inherit;
}

.tile-panel__content h2 {
  margin: 0 0 10px;
}

.tile-panel__hd {
  margin-bottom: 5px;
}

.tile-panel__location {
  font-style: italic;
  margin-top: -5px;
  margin-bottom: 10px;
}

.tile-panel__preferred {
  float: right;
  color: #a80000;
  font-weight: 700;
}

.tile-panel__summary p {
  margin: 0;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .tile-panel--float {
    float: left;
    width: 40%;
    width: calc(50% - 10px);
    margin-right: 20px;
  }

  .tile-panel--float:nth-child(even) {
    margin-right: 0;
  }
}
@media (min-width: 1280px) {
  .tile-panel--float {
    width: 30%;
    width: calc(33% - 14px);
  }

  .tile-panel--narrow {
    width: 40%;
    width: calc(50% - 10px);
  }

  .tile-panel--float:nth-child(even) {
    margin-right: 20px;
  }

  .tile-panel--float:nth-child(3n) {
    margin-right: 0;
  }

  .tile-panel--narrow:nth-child(3n) {
    margin-right: 20px;
  }

  .tile-panel--narrow:nth-child(even) {
    margin-right: 0;
  }
}
/*------------------------------------*\
    $SHARE BUTTONS
\*------------------------------------*/
.share-btns__btn {
  float: left;
  margin-right: 10px;
}

/*------------------------------------*\
    $BLOG
\*------------------------------------*/
.entry {
  margin-bottom: 50px;
}

.sidebar-nav__inner > .widget:first-child .widget-title {
  margin-top: 0;
}

.entry__thumbnail {
  margin-bottom: 10px;
}

.entry__thumbnail img {
  max-width: 100%;
  height: auto;
}

.entry__meta {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.entry__meta > li {
  display: inline-block;
}

.entry__meta--date-author > li {
  font-style: italic;
}

.entry__meta--categories > li {
  font-weight: 700;
}

.entry__meta > li:not(:last-child):after {
  content: '|';
  display: inline-block;
  margin: 0 5px;
}

.entry__meta > li > a {
  color: #565044;
}

.entry__hd a {
  color: #a80000;
}

.entry__meta a {
  color: #565044;
}

.entry__content p {
  margin-bottom: 24px;
  font-size: 15px;
}

.entry__content a {
  color: #a80000;
}

.entry__comments {
  padding: 40px 0 0;
}

.comment {
  padding: 15px 0;
  border-bottom: 1px solid rgba(108, 64, 0, 0.3);
}

.comment:last-child {
  border-bottom: none;
}

.comment__author {
  font-weight: 700;
  margin-bottom: 0;
}

.comment__date {
  margin: 0;
  font-style: italic;
}

.comment-form label {
  display: block;
  margin-bottom: 3px;
}

.comment-form input {
  display: block;
  width: 100%;
  max-width: 320px;
  padding: 3px;
}

.comment-form input[type="checkbox"] {
  display: inline-block;
  margin-right: 3px;
}

.comment-form input[type="checkbox"] + label {
  display: inline-block;
}

.comment-form-comment textarea {
  display: block;
  width: 100%;
  max-width: 460px;
  padding: 3px;
}

.comment-form .submit {
  width: auto;
}

.wp-caption {
  margin-bottom: 24px;
}

.wp-caption img[class*="wp-image-"] {
  display: block;
  margin: 0;
}

.wp-caption {
  color: #767676;
}

.wp-caption-text {
  box-sizing: border-box;
  font-size: 12px;
  font-style: italic;
  line-height: 1.5;
  margin: 9px 0;
}

div.wp-caption .wp-caption-text {
  padding-right: 10px;
}

div.wp-caption.alignright img[class*="wp-image-"],
div.wp-caption.alignright .wp-caption-text {
  padding-left: 10px;
  padding-right: 0;
}

/* Responsive images. Fluid images for posts */
.entry__content img,
.entry-summary img {
  max-width: 100%;
}

/**
 * Make sure images with WordPress-added height and width attributes are
 * scaled correctly.
 */
.entry__content img,
.entry-summary img,
img[class*="align"],
img[class*="wp-image-"],
img[class*="attachment-"] {
  height: auto;
}

/* Alignment */
.alignleft {
  float: left;
}

.alignright {
  float: right;
}

.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

blockquote.alignleft,
figure.wp-caption.alignleft,
img.alignleft {
  margin: 7px 24px 7px 0;
}

.wp-caption.alignleft {
  margin: 7px 14px 7px 0;
}

blockquote.alignright,
figure.wp-caption.alignright,
img.alignright {
  margin: 7px 0 7px 24px;
}

.wp-caption.alignright {
  margin: 7px 0 7px 14px;
}

blockquote.aligncenter,
img.aligncenter,
.wp-caption.aligncenter {
  margin-top: 7px;
  margin-bottom: 7px;
}

/*
* Media Queries
*/
/*------------------------------------*\
    $CAMPAIGNS
\*------------------------------------*/
.content-blocks.content-blocks--landing-pages {
  max-width: 710px;
}

.content-block.content-block--landing-pages {
  padding-bottom: 20px;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
}
.content-block.content-block--landing-pages:first-child {
  margin-top: 0;
}
.content-block.content-block--landing-pages:last-child {
  border: none;
}

.page-left-sidebar.page-left-sidebar--landing-pages .page-left-sidebar__tracker > div:first-child {
  padding-top: 0;
}
.page-left-sidebar.page-left-sidebar--landing-pages .page-left-sidebar__tracker > div:first-child h3 {
  margin-top: 0;
}

/*------------------------------------*\
    $CONTENTS - CHROME
\*------------------------------------*/
/**
* CONTENTS............You're reading it!
* STRUCTURE...........Holds it all together
* HEADER..............Header
* BREADCRUMB..........Breadcrumb navigation
* FOOTER..............Footer
* CONTACT.............Contact module
*/
/*------------------------------------*\
    $STRUCTURE
\*------------------------------------*/
body {
  background-image: url('/img/site/layout/chrome/dust.png?1711536199');
  background-repeat: repeat;
  background-size: 200px 150px;
}

.page-wrapper {
  margin-bottom: 84px;
}
.page-wrapper ~ .page-footer {
  padding-bottom: 84px;
}
.page-wrapper.page-wrapper--no-banner {
  margin-bottom: 0;
}
.page-wrapper.page-wrapper--no-banner .page-footer {
  padding-bottom: 52px;
}
@media (min-width: 1440px) {
  .page-wrapper.page-wrapper--no-banner .page-footer {
    padding-bottom: 0;
  }
}

/*------------------------------------*\
    $HEADER
\*------------------------------------*/
.page-header {
  position: relative;
  z-index: 10;
  background: #a80000;
  box-shadow: 0 7px 2px rgba(0, 0, 0, 0.3);
  color: #fff;
}
.page-header a {
  color: #fff;
}

.page-header__inner {
  position: relative;
  padding: 20px 18px;
}

.page-header__logo {
  display: block;
  margin: 0 auto;
}

.nav-toggle {
  position: absolute;
  top: 12px;
  right: auto;
  bottom: auto;
  left: 0px;
  padding: 15px;
}

.nav-toggle span {
  display: block;
}

.nav-wrapper {
  display: none;
}
.nav-wrapper.active {
  display: block;
}

.primary-nav li {
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
  padding: 14px 0;
  text-transform: uppercase;
  border-top: 1px dashed rgba(255, 255, 255, 0.3);
}

.secondary-nav {
  /* margin-top: 18px; */
}
.secondary-nav li {
  font-size: 1em;
  line-height: 1.4666666667em;
  padding: 14px 0;
  line-height: 16px;
  text-transform: uppercase;
}
.secondary-nav > ul > li {
  border-top: 1px dashed rgba(255, 255, 255, 0.3);
}
.secondary-nav a:after {
  display: none;
}

.search-toggle {
  position: absolute;
  top: 23px;
  right: 19px;
  bottom: auto;
  left: auto;
}

.primary-search {
  display: none;
  margin-top: 18px;
  padding: 10px 0;
  border-top: 1px dashed rgba(255, 255, 255, 0.3);
  border-bottom: 1px dashed rgba(255, 255, 255, 0.3);
}
.primary-search input[type=text] {
  padding: 3px;
  width: 100%;
  color: #565044;
}
.primary-search button {
  float: right;
  margin: 3px 0 0 10px;
}
.primary-search.active {
  display: block;
}

.primary-search__form {
  overflow: hidden;
}

.primary-search__input {
  width: auto;
  overflow: hidden;
}

/*
* Media Queries
*/

/* Desktop header elements (1024px+) */
@media screen and (min-width: 1024px) {
  .page-header {
    min-height: 90px;
  }

  .page-header__inner {
    padding: 14px 0 0;
  }

  .page-header__logo {
    position: absolute;
    top: 20px;
    right: auto;
    bottom: auto;
    left: 18px;
    background-position: 0 -143px;
    height: 50px;
    width: 281px;
  }

  .nav-toggle,
  .search-toggle {
    display: none;
  }

  .nav-wrapper {
    display: block;
  }

  .primary-nav {
    display: block;
  }
  .primary-nav li {
    display: inline;
    text-transform: none;
    border-top: none;
  }
  .primary-nav li:last-child {
    margin-right: 0;
    padding-right: 0;
  }
  .primary-nav li:last-child:after {
    display: none;
  }
  .primary-nav li:after {
    content: "";
    margin: 0 7px;
  }
}

  .secondary-nav {
    display: block;
    margin: 85px 0 0 18px;
  }
  .secondary-nav li {
    padding: 0;
    display: inline-block;
    margin-right: 29px;
    vertical-align: bottom;
    border-top: none !important;
  }
  .secondary-nav li:last-child {
    margin-right: 0;
  }
  .secondary-nav a:after {
    /* position: relative;
    top: 2px;
    right: auto;
    bottom: auto;
    left: 10px;
    display: inline-block;
    content: ""; */
  }
  .secondary-nav > ul > li {
    position: relative;
    padding-bottom: 10px;
  }

  .primary-search {
    /* position: absolute;
    top: 50px;
    right: 18px;
    bottom: auto;
    left: auto; */
    display: block;
    padding: 0;
    margin-top: 0;
    border-top: none;
    border-bottom: none;
  }
}
@media (min-width: 980px) {
  .page-header__logo {
    position: absolute;
    top: 20px;
    right: auto;
    bottom: auto;
  }

  .primary-nav {
    /* position: absolute;
    top: 12px;
    right: auto;
    bottom: auto;
    left: 340px; */
  }

  .secondary-nav {
    /* position: absolute;
    top: 55px;
    right: auto;
    bottom: auto;
    left: 340px; */
    margin: 0;
  }
  .secondary-nav li {
    max-width: 128px;
  }
  .secondary-nav li:nth-child(3) {
    display: none;
  }
}
@media (min-width: 1200px) {
  .secondary-nav li:nth-child(3) {
    display: inline-block;
  }
}
@media (min-width: 1280px) {
  .secondary-nav {
    /* top: 54px; */
  }
  .secondary-nav li {
    max-width: none;
  }
  .secondary-nav li:nth-child(3) {
    display: inline-block;
  }
}
/*------------------------------------*\
    $BREADCRUMB
\*------------------------------------*/
.breadcrumb {
  display: none;
  background-image: url('/img/site/layout/chrome/noise-lines.png?1711536199');
  background-repeat: repeat;
  background-size: 60px 59px;
}
.breadcrumb li {
  float: left;
}
.breadcrumb li::after {
  display: inline-block;
  margin: 0 5px 0 7px;
  content: "/";
}
.breadcrumb li:last-child::after {
  content: "";
}
.breadcrumb a {
  color: #6c4000;
}

.breadcrumb__inner {
  padding: 10px 18px;
}

/*
* Media Queries
*/
/* Keep breadcrumb media query at 768px as it's not related to header/menu */
@media (min-width: 768px) {
  .breadcrumb {
    display: block;
  }
}
/*------------------------------------*\
    $LEFT SIDEBAR
\*------------------------------------*/
.page-left-sidebar {
  position: absolute;
  top: 0px;
  right: auto;
  bottom: auto;
  left: 0px;
  display: none;
  z-index: 10;
  width: 230px;
}
.page-left-sidebar h3 {
  color: #6c4000;
}
.page-left-sidebar .enews-module,
.page-left-sidebar .quote-module,
.page-left-sidebar .call-us-module {
  padding: 0 10px 10px;
  border-style: solid;
  border-color: #565044;
  border-width: 0 0 1px;
}
.page-left-sidebar .enews-module {
  border-bottom: none;
}
.page-left-sidebar .enews-module__form input[type=text],
.page-left-sidebar .enews-module__form input[type=email] {
  width: 100%;
}
.page-left-sidebar .call-us-module a {
  font-size: 1.2em;
  line-height: 1.2222222222em;
}
.page-left-sidebar .call-us-module a::after {
  top: 0;
}

.page-left-sidebar--blog {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: auto;
  left: auto;
}

.page-left-sidebar__tracker {
  position: relative;
}

/*
* Media Queries
*/
@media (min-width: 980px) {
  .page-left-sidebar {
    display: block;
  }
}
/*------------------------------------*\
    $FOOTER
\*------------------------------------*/
.page-footer {
  background-image: url('/img/site/layout/chrome/noise-lines.png?1711536199');
  background-repeat: repeat;
  background-size: 60px 59px;
  border-top: 1px solid #fff;
}

.page-footer__inner {
  position: relative;
  padding: 10px 18px 40px;
}

.page-footer__about {
  margin-bottom: 20px;
}
.page-footer__about a {
  color: #a80000;
}

.page-footer__holidays {
  margin-bottom: 20px;
}

.page-footer__holidays-inner ul > li {
  margin-bottom: 10px;
}

.page-footer__thirdp {
  clear: both;
  max-width: 365px;
}

.bv-community {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  row-gap: 12px;
}

.page-footer__nav {
  padding: 15px 0 0;
  clear: both;
  border-style: dashed;
  border-color: #565044;
  border-width: 1px 0 0;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  gap: 12px;
}

.page-footer__copyright {
  clear: both;
}

.thirdp-associations a {
  margin: 15px 12px 0 0;
  display: inline-block;
  vertical-align: middle;
}

.bv-community {
  margin-bottom: 20px;
}
.bv-community li {
  float: left;
}
.bv-community a {
  display: block;
  margin: 0 12px 0 0;
}

.bv-community__wordpress {
  border-radius: 50%;
  overflow: hidden;
  background-color: #27769A;
}

.bv-community__facebook {
  border-radius: 50%;
  overflow: hidden;
  background-color: #1877F2;
}

.bv-community__twitter {
  border-radius: 50%;
  overflow: hidden;
  background-color: #000;
}

.bv-community__youtube {
  border-radius: 50%;
  overflow: hidden;
  background-color: #FF0000;
}

.bv-community__linkedin {
  border-radius: 50%;
  overflow: hidden;
  background-color: #0966C2;
}

.bv-community__instagram {
  border-radius: 50%;
  overflow: hidden;
}

.footer-nav ul {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  gap: 10px;
}
.footer-nav a {
  color: #565044;
}

/*
* Media Queries
*/
@media (min-width: 450px) {
  .thirdp-associations__discover-america {
    float: right;
    display: block;
  }
}
@media (min-width: 500px) {
  .page-footer__copyright {
    text-align: right;
  }

  .page-footer__nav {
    -ms-flex-direction: row;
        flex-direction: row;
    -ms-flex-pack: justify;
        justify-content: space-between;
  }
}
@media (min-width: 768px) {
  .page-footer__holidays-inner > ul > li {
    float: left;
    width: 50%;
  }

  .thirdp-associations {
    float: right;
    text-align: right;
  }
  .thirdp-associations a {
    margin: 0 0 0 12px;
  }

  .bv-community {
    float: left;
  }
  .bv-community li {
    float: left;
  }
  .bv-community a {
    margin: 0 12px 12px 0;
  }
}
@media (min-width: 880px) {
  .footer-nav ul {
    -ms-flex-direction: row;
        flex-direction: row;
    gap: 16px;
  }
}
@media (min-width: 980px) {
  .page-footer__about {
    min-height: 320px;
  }

  .page-footer__holidays-inner > ul > li {
    width: 33.3333%;
  }
}
@media (min-width: 1280px) {
  .page-footer__holidays-inner > ul > li {
    float: left;
    width: 40%;
    width: calc(50% - 10px);
  }

  .page-footer__holidays-inner > ul > li {
    width: 25%;
  }
}
/*------------------------------------*\
    $CONTACT
\*------------------------------------*/
.page-wrapper__contact-module {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 12;
  background: rgba(238, 238, 238, 0.9);
  border-top: 1px solid rgba(168, 0, 0, 0.4);
}
.page-wrapper__contact-module h3 {
  font-size: 1.0666666667em;
  line-height: 1.375em;
  margin: 5px 0;
}
.page-wrapper__contact-module .call-us-module p {
  margin: 0 0 5px;
}
.page-wrapper__contact-module .call-us-module p:first-child {
  margin-top: 10px;
}
.page-wrapper__contact-module .call-us-module p:last-child {
  margin-bottom: 10px;
}
.page-wrapper__contact-module .call-us-module p:last-child span {
  display: none;
}
.page-wrapper__contact-module .call-us-module h3 {
  display: none;
}
.page-wrapper__contact-module .quote-module {
  float: right;
}
.page-wrapper__contact-module .quote-module a span {
  display: none;
}
.page-wrapper__contact-module .quote-module__intro,
.page-wrapper__contact-module .quote-module__bonded {
  display: none;
}
.page-wrapper__contact-module .enews-module {
  max-width: 800px;
}
.page-wrapper__contact-module .enews-module input[type=text],
.page-wrapper__contact-module .enews-module input[type=email] {
  float: left;
  margin-right: 10px;
  width: calc(50% - 5px);
}
.page-wrapper__contact-module .enews-module input[name=last_name] {
  margin-right: 0;
}
.page-wrapper__contact-module .enews-module .enews-module__form button {
  clear: none;
}
.page-wrapper__contact-module .enews-module__our-ideas {
  display: none;
}

.page-wrapper__contact-module-inner {
  padding: 0 10px;
  overflow: hidden;
  max-width: 1280px;
  margin: 0 auto;
}

/*
* Media Queries
*/
@media (min-width: 380px) {
  .page-wrapper__contact-module .call-us-module p:last-child span {
    display: inline;
  }
}
@media (min-width: 768px) {
  .page-wrapper__contact-module h3 {
    font-size: 1.2em;
    line-height: 1.2222222222em;
    margin: 10px 0;
  }
  .page-wrapper__contact-module .enews-module {
    float: left;
    margin-bottom: 10px;
    width: calc(100% - 230px);
  }
  .page-wrapper__contact-module .enews-module input[type=text],
  .page-wrapper__contact-module .enews-module input[type=email] {
    width: calc(33% - 34px);
  }
  .page-wrapper__contact-module .enews-module input[name=last_name] {
    margin-right: 10px;
  }
  .page-wrapper__contact-module .call-us-module {
    padding-top: 12px;
  }
  .page-wrapper__contact-module .call-us-module p:first-child {
    margin-top: 0;
  }
  .page-wrapper__contact-module .call-us-module h3 {
    display: block;
  }
}
@media (min-width: 980px) {
  .page-wrapper__contact-module .enews-module input[type=text],
  .page-wrapper__contact-module .enews-module input[type=email] {
    width: calc(33% - 37px);
  }
  .page-wrapper__contact-module .enews-module input[name=last_name] {
    margin-right: 10px;
  }
}
/*
 * Feefo Widget
*/
.feefo-widget {
  margin-top: 25px;
  margin-bottom: 25px;
}

/*------------------------------------*\
    $CONTACT
\*------------------------------------*/
.content-blocks.content-blocks--contact-us {
  max-width: 710px;
}
.content-blocks.content-blocks--contact-us .content-block:first-child {
  margin-top: 0;
}
.content-blocks.content-blocks--contact-us .content-block:first-child h2 {
  margin-top: 0;
}

.content-block__cols__col.content-block__cols__col--address {
  padding-right: 50px;
}

.content-block__cols__col.content-block__cols__col--hours {
  display: block;
}

.contact-form .input {
  margin-bottom: 10px;
}
.contact-form label {
  float: left;
  width: 200px;
  max-width: 100%;
  padding: 3px 0;
}
.contact-form input[type=text],
.contact-form select,
.contact-form textarea {
  width: 300px;
  max-width: 100%;
}
.contact-form input[type=text] {
  padding: 3px;
}
.contact-form .checkbox input[type=checkbox] {
  margin-right: 10px;
}
.contact-form .checkbox label {
  float: none;
  display: inline-block;
  vertical-align: top;
  width: 250px;
}
.contact-form .radio {
  position: relative;
}
.contact-form .radio input[type=radio] {
  float: left;
  margin: 5px 10px 0 0;
  clear: both;
}
.contact-form .radio label {
  float: none;
  display: inline-block;
  vertical-align: top;
  width: 280px;
  max-width: 100%;
}
.contact-form button {
  margin-bottom: 10px;
  font-weight: 700;
  text-transform: uppercase;
}
.contact-form .error-message {
  padding: 2px 0;
  color: #a80000;
  font-weight: 700;
}

/*
* Media Queries
*/
@media (min-width: 530px) {
  .content-block__cols__col.content-block__cols__col--hours {
    display: table-cell;
  }

  .contact-form .checkbox input[type=checkbox] {
    margin: 10px 10px 0 200px;
  }
  .contact-form .radio input[type=radio] {
    margin: 5px 10px 0 200px;
  }
  .contact-form .radio label {
    width: 280px;
  }
  .contact-form .radio label:first-child {
    position: absolute;
    top: 0px;
    right: auto;
    bottom: auto;
    left: 0px;
    width: 190px;
  }
  .contact-form button {
    margin-left: 200px;
  }
  .contact-form .text .error-message,
  .contact-form .select .error-message,
  .contact-form .textarea .error-message,
  .contact-form .checkbox .error-message {
    margin-left: 200px;
  }
}
.preferred-date {
  opacity: 0;
  overflow: hidden;
  height: 10px;
}

/*------------------------------------*\
$FAQs
\*------------------------------------*/
.faqs-index {
  float: left;
  max-width: 710px;
}

.faqs-index__questions li {
  margin-bottom: 10px;
}

.faqs-index__answers {
  margin: 30px 0;
}

.faqs-index__answers__answer {
  border-style: dashed;
  border-color: #565044;
  border-width: 1px 0 0;
}
.faqs-index__answers__answer p {
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
}

.faqs-index__answers__top a {
  font-size: 1em;
  line-height: 1.5714285714em;
  text-transform: uppercase;
}

.page-right-sidebar.page-right-sidebar--faqs {
  margin-top: 0;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .faqs-index {
    padding: 0 18px;
  }
}
@media (min-width: 980px) {
  .faqs-index {
    padding: 0;
  }
}
/*------------------------------------*\
    $HOLIDAYS
\*------------------------------------*/
.content-blocks.content-blocks--holidays {
  max-width: 710px;
}

/*
* Media Queries
*/
/*------------------------------------*\
    $HOME CONTENTS
\*------------------------------------*/
/**
* CONTENTS..........................You're reading it!
* HOT, FEATURED & ITINERNARIES......What's hot, Featured movies, and Fav Itinernaries
* ENEWS AND QUOTE...................Enews Form and Quote link
* SEE, IDEAS & EXPLORE..............See and do, Ideas and Inspiration, and Explore USA
*/
/*------------------------------------*\
    $HOT, FEATURED & ITINERNARIES
\*------------------------------------*/
/*
* Media Queries
*/
/*------------------------------------*\
    $LIGHTBOX
\*------------------------------------*/
.ui-modal {
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
}
.ui-modal .r {
  margin-top: 10px;
}

.ui-modal-close {
  position: absolute;
  top: 10px;
  right: 10px;
  bottom: auto;
  left: auto;
  text-transform: uppercase;
  font-weight: 700;
}

/*------------------------------------*\
$PAGES
\*------------------------------------*/
.content-blocks.content-blocks--pages {
  max-width: 710px;
}
.content-blocks.content-blocks--pages > .content-block:first-child {
  margin-top: 0;
}

/*------------------------------------*\
    $PRESS RELEASES
\*------------------------------------*/
.press-release {
  padding-bottom: 20px;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
}
.press-release.press-release--index {
  padding-bottom: 0;
}
.press-release.press-release--index:first-child {
  border-style: dashed;
  border-color: #565044;
  border-width: 1px 0 1px;
}

.press-release__text {
  float: left;
  width: 100%;
  margin-right: -110px;
}
.press-release__text h2 {
  font-size: 1.3333333333em;
  line-height: 1.65em;
}
.press-release__text h2 a {
  color: #6c4000;
}
.press-release__text p {
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
}

.press-release__inner {
  margin-right: 110px;
}

.press-release__text__link a {
  font-size: 1em;
  line-height: 1.5714285714em;
  text-transform: uppercase;
}

.press-release .share-btns {
  margin-top: 1.5714285714em;
  margin-bottom: 1.5714285714em;
}

.press-release__image {
  float: right;
  margin: 20px 0;
  width: 100px;
}

.content-blocks.content-blocks--press-releases .content-block:first-child {
  margin-top: 0;
}

.image-viewer.image-viewer--press-releases {
  display: none;
  float: left;
}
.image-viewer.image-viewer--press-releases .image-viewer__main {
  height: 433px;
}
.image-viewer.image-viewer--press-releases .image-viewer__wrapper__images {
  width: 417px;
}

.press-releases-back {
  margin: 30px 0;
  clear: both;
}
.press-releases-back a {
  text-transform: uppercase;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .page-content-body--press-releases {
    padding: 0 18px !important;
  }

  .press-release__text {
    margin-right: -205px;
  }
  .press-release__text h2 {
    font-size: 1.7333333333em;
    line-height: 1.2692307692em;
  }

  .press-release__inner {
    margin-right: 205px;
  }

  .press-release__image {
    width: auto;
  }

  .press-release__text__link {
    float: left;
  }

  .press-release .share-btns {
    clear: none;
    float: right;
    margin-bottom: 0;
  }

  .image-viewer.image-viewer--press-releases {
    display: block;
  }
}
@media (min-width: 980px) {
  .press-releases-index {
    padding: 0;
  }

  .content-blocks.content-blocks--press-releases {
    float: left;
    width: 480px;
  }

  .image-viewer.image-viewer--press-releases {
    float: right;
  }
}
/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
* CONTENTS...........................You're reading it!
* VARS...............................Colors, widths, heights, etc
* SECTION............................Default styles for destinations, itineraries etc
* NAVIGATION.........................Section tabs
* OVERVIEW...........................Destination overview page
* INDEX..............................Index for itineraries, activities
* VIEW...............................Detail page for itineraries, activities
* ACCOMMODATION......................Accommodation
* ITINERARIES ...................... Itineraries
* VIDEOS.............................Video Gallery
* IMAGES.............................Images Gallery
* RIGHT SIDEBAR......................Right side bar
* RELATED HOLIDAYS & ACTIVITIES......Related records
*/
/*------------------------------------*\
    $VARS
\*------------------------------------*/
/*------------------------------------*\
    $SECTION
\*------------------------------------*/
.page-content-body {
  padding: 0 18px;
}
.page-content-body .page-content-body__inner {
  position: relative;
}
.page-content-body .image-and-map {
  display: none;
  position: relative;
  z-index: 11;
}
.page-content-body .image-and-map--itinerary {
  display: block;
}
.page-content-body .image-and-map__image {
  width: 355px;
  height: 340px;
  overflow: hidden;
}
.page-content-body .image-and-map__image--full-image {
  width: 100%;
  height: auto;
}
.page-content-body .image-and-map__map-wrapper {
  width: calc(100% - 365px);
  height: 340px;
}
.page-content-body .image-and-map__map-wrapper--full-width {
  width: 100%;
  max-width: none;
}
.page-content-body .image-and-map__map-wrapper--fixed {
  background-color: #fff;
  border-right: 0;
  border: 1px solid rgba(86, 80, 68, 0.2);
  bottom: 120px;
  left: 100%;
  padding: 10px 0 10px 10px;
  position: fixed;
  top: 10%;
  transition: 0.6s;
  width: 80%;
  z-index: 999999;
  height: 340px;
  display: block; /* Ensure it's displayed */
}
/* Ensure fixed map is positioned relative to viewport, not parent container */
.image-and-map__map-wrapper--fixed {
  position: fixed !important;
  z-index: 999999 !important;
}
.page-content-body .image-and-map__map-wrapper--fixed.active {
  left: 20%;
}
.page-content-body .image-and-map__hide {
  display: block;
  position: absolute;
  top: 10px;
  right: 10px;
  background: #fff;
  border: 1px solid rgba(86, 80, 68, 0.2);
  padding: 5px 10px;
  cursor: pointer;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .page-content-body {
    padding: 0;
  }
  .page-content-body .image-and-map {
    display: block;
    margin: 0 0 0 18px;
  }
}
@media (min-width: 980px) {
  .page-content-body {
    min-height: 900px;
  }
  .page-content-body .image-and-map {
    float: right;
    margin: 0;
    width: calc(100% - 260px);
  }
}
/*------------------------------------*\
    $NAVIGATION
\*------------------------------------*/
.destinations-nav {
  display: none;
  position: relative;
  top: -10px;
  padding-top: 30px;
  background-image: url('/img/site/layout/chrome/noise-lines.png?1711536199');
  background-repeat: repeat;
  background-size: 60px 59px;
}
.destinations-nav ul {
  margin-left: 28px;
  border-top: 1px solid #565044;
  border-bottom: 3px solid #565044;
}
.destinations-nav li {
  float: left;
}
.destinations-nav li:first-child button {
  border-left: 1px dashed #565044;
}
.destinations-nav .active {
  margin-bottom: -3px;
}
.destinations-nav .active button {
  height: 83px;
  background-image: url('/img/site/layout/chrome/dust.png?1711536199');
  background-repeat: repeat;
  background-size: 200px 150px;
}
.destinations-nav button {
  display: block;
  height: 80px;
  background: none;
  padding: 0 15px;
  border-color: #565044;
  border-width: 0 1px 0 0;
  border-style: none dashed;
  color: #565044;
  font-size: 1.0666666667em;
  line-height: 1.375em;
  text-transform: uppercase;
}
.destinations-nav button:focus {
  outline: none;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .destinations-nav {
    display: block;
  }
}
@media (min-width: 980px) {
  .destinations-nav ul {
    float: right;
    width: calc(100% - 260px);
    margin-left: 0;
  }
}
/*------------------------------------*\
    $OVERVIEW
\*------------------------------------*/
.page-content-body__content {
  margin-bottom: 2.9333333333em;
}

.content-blocks .content-block:last-child {
  margin-bottom: 0;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .page-content-body__content {
    padding: 0 18px;
  }

  .content-blocks {
    width: auto;
    overflow: hidden;
  }
}
@media (min-width: 980px) {
  .page-content-body__content {
    float: right;
    min-height: 680px;
    width: calc(100% - 260px);
    padding: 0;
  }

  .page-content-body__content--blog {
    float: left;
  }
}
/*------------------------------------*\
    $INDEX
\*------------------------------------*/
.content-blocks.content-blocks--index .content-block {
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
  padding-bottom: 20px;
}
.content-blocks.content-blocks--index .content-block h2 {
  font-size: 1.4em;
  line-height: 1.5714285714em;
  margin-bottom: 0.5238095238em;
}
.content-blocks.content-blocks--index .content-block h3 {
  font-size: 1.0666666667em;
  line-height: 2.0625em;
}

.content-block__text__location {
  color: #a80000;
  font-size: 1.0666666667em;
  line-height: 2.0625em;
}

.itineraries {
  padding-top: 30px;
}

/*------------------------------------*\
    $VIEW
\*------------------------------------*/
.sub-section-header h2 {
  margin-bottom: 0;
  font-size: 1.7333333333em;
  line-height: 1.6923076923em;
  color: #6c4000;
}
.sub-section-header .sub-section-header__price {
  margin-top: 0;
  color: #a80000;
  font-size: 1.1333333333em;
  line-height: 1.9411764706em;
  text-transform: uppercase;
}

/*------------------------------------*\
    $ACCOMMODATION
\*------------------------------------*/
.content-blocks--index.content-block--accommodation .content-block {
  border-bottom: none;
  padding-bottom: 0;
}
.content-blocks--index.content-block--accommodation .content-block .content-block__text > h2 {
  margin-bottom: 0;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .content-blocks--accommodation {
    width: calc(100% - 260px);
    float: left;
  }
}
/*------------------------------------*\
    $ITINERARIES
\*------------------------------------*/
.content-blocks.content-blocks--itinerary .content-block:first-child {
  margin-top: 0;
}

.content-blocks.content-blocks--itinerary-day {
  max-width: 710px;
}

/*------------------------------------*\
    $VIDEOS
\*------------------------------------*/
.youtube-videos {
  margin-top: 30px;
}

.youtube-videos__video {
  margin: 0 0 40px;
}

.youtube-videos__iframe-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-top: 71%;
}

.youtube-videos__iframe {
  position: absolute;
  top: 0;
  left: 0;
  /* right: 0; */
  /* bottom: 0; */
  width: 100%;
  height: 100%;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .youtube-videos__video {
    float: left;
    width: 40%;
    margin: 0 20px 40px 0;
  }
  .youtube-videos__video:nth-child(odd) {
    clear: left;
  }
  .youtube-videos__video:nth-child(even) {
    margin-right: 0;
  }
}
/*------------------------------------*\
    $IMAGES
\*------------------------------------*/
.image-viewer.image-viewer--section-images {
  width: 616px;
  margin-top: 30px;
}
.image-viewer.image-viewer--section-images .image-viewer__wrapper__images__image {
  margin-right: 10px;
}

/*------------------------------------*\
    $RIGHT SIDEBAR
\*------------------------------------*/
.page-right-sidebar {
  display: none;
  float: right;
  width: 230px;
  margin-top: 30px;
  padding: 0 10px;
}
.page-right-sidebar .enews-module__form input[type=text],
.page-right-sidebar .enews-module__form input[type=email] {
  width: 100%;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .page-right-sidebar {
    display: block;
    margin-left: 22px;
  }

  .page-right-sidebar .testimonial-extract__content {
    padding-left: 0;
  }
  .page-right-sidebar .testimonial-extract__content::before {
    display: none;
  }
}
/*------------------------------------*\
    $RELATED HOLIDAYS & ACTIVITIES
\*------------------------------------*/
.holidays-and-activities {
  margin-top: 2.9333333333em;
  margin-bottom: 2.9333333333em;
  padding: 0 18px;
}

/*
* Media Queries
*/
@media (min-width: 980px) {
  .holidays-and-activities {
    padding: 0 10px;
  }
}
/*------------------------------------*\
    $SEARCH
\*------------------------------------*/
.search-results {
  padding: 0 18px;
}

.search-results__border {
  height: 1px;
  border-style: dashed;
  border-color: #565044;
  border-width: 1px 0 0 0;
}

.search-results__results {
  margin-top: 0.7333333333em;
}

.search-results__results__summary {
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
}

.search-results__results__result {
  margin-bottom: 0.7333333333em;
  padding-bottom: 1.4666666667em;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px 0;
}

.search-results__results__result__title {
  font-size: 1.2em;
  line-height: 1.2222222222em;
}
.search-results__results__result__title a {
  color: #6c4000;
}

.search-results__results__result__link {
  display: none;
  margin-bottom: 0.3666666667em;
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
}
.search-results__results__result__link a {
  color: #a80000;
}

.search-results__results__result__extract {
  font-size: 0.9333333333em;
  line-height: 1.5714285714em;
}

.page-right-sidebar.page-right-sidebar--search-results {
  margin-top: 0.7333333333em;
  margin-bottom: 40px;
}

.primary-search.primary-search--page-right-sidebar {
  position: relative;
  float: none;
  padding: 20px 10px;
  background: rgba(247, 245, 241, 0.9);
}
.primary-search.primary-search--page-right-sidebar h2 {
  margin-top: 0;
  margin-bottom: 0.3666666667em;
  color: #a80000;
  font-weight: 700;
  font-size: 1.2666666667em;
  line-height: 1.7368421053em;
}
.primary-search.primary-search--page-right-sidebar input[type=text] {
  width: 165px;
}

/*
* Media Queries
*/
@media (min-width: 768px) {
  .search-results__results {
    float: left;
    width: 100%;
    margin-right: -260px;
  }

  .search-results__inner {
    margin-right: 260px;
  }

  .search-results__results__result__link {
    display: block;
  }
}
/*
* Media Queries
*/
@media (min-width: 980px) {
  .search-results {
    padding: 0;
  }
}
/*------------------------------------*\
    $TESTIMONIALS
\*------------------------------------*/
.content-blocks.content-blocks--testimonials {
  max-width: 710px;
}
.content-blocks.content-blocks--testimonials.content-blocks--section {
  max-width: 460px;
}
.content-blocks.content-blocks--testimonials .content-block__image img {
  max-width: 95px;
}

.content-blocks.content-blocks--testimonial {
  max-width: 710px;
}
.content-blocks.content-blocks--testimonial.content-blocks--section {
  max-width: 460px;
}
.content-blocks.content-blocks--testimonial .content-block:first-child {
  margin-top: 0;
}

.sub-section-header.sub-section-header-testimonial h2 {
  margin-bottom: 0.8461538462em;
}

.page-right-sidebar.page-right-sidebar--testimonial {
  margin-top: 0;
}

/*------------------------------------*\
$WHATS HOT
\*------------------------------------*/
.page-content-header--whats-hot h1 {
  font-weight: 700;
}

.whats-hot-intro {
  font-size: 1.2em;
  line-height: 1.2222222222em;
  margin: 0 0 30px;
}

.whats-hot-index__hot {
  padding: 0 10px;
}
.whats-hot-index__hot.whats-hot-index__hot--read-more {
  display: none;
  margin-bottom: 30px;
}
.whats-hot-index__hot.whats-hot-index__hot--read-more a {
  display: block;
  padding-bottom: 10px;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
  text-transform: uppercase;
}
.whats-hot-index__hot h2 {
  font-size: 1.2em;
  line-height: 1.4666666667em;
  font-weight: 700;
}

.whats-hot-index__image {
  float: left;
  width: 40%;
  margin: 0 10px 10px -10px;
}

.whats-hot-index__intro {
  clear: both;
}

.whats-hot-index__read-more {
  margin-bottom: 30px;
  clear: both;
}
.whats-hot-index__read-more a {
  display: block;
  padding-bottom: 10px;
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px;
  text-transform: uppercase;
}

.whats-hot-main-image {
  float: left;
  margin-bottom: 20px;
}
.whats-hot-main-image > div {
  display: none;
}
.whats-hot-main-image > div:first-child {
  display: block;
}

.whats-hot-view {
  clear: both;
}
.whats-hot-view li {
  color: #a80000;
}

.whats-hot-view__intro {
  border-style: dashed;
  border-color: #565044;
  border-width: 0 0 1px 0;
  clear: both;
}
.whats-hot-view__intro > *:last-child {
  margin-bottom: 30px;
}

.whats-hot-view__details {
  clear: both;
}
.whats-hot-view__details > *:first-child {
  margin-top: 30px;
}

.whats-hot-view__images {
  float: right;
  width: 230px;
  margin: 30px 0 10px 30px;
}
.whats-hot-view__images > div {
  margin-bottom: 20px;
}

.content-blocks.content-blocks--whats-hot {
  float: none;
  width: 100%;
}

.whats-hot-view__back {
  border-style: dashed;
  border-color: #565044;
  border-width: 1px 0 0;
  padding-top: 30px;
  margin-top: 30px;
}
.whats-hot-view__back a {
  font-size: 1em;
  line-height: 1.5714285714em;
  text-transform: uppercase;
}

/*
* Media Queries
*/
@media (min-width: 450px) {
  .whats-hot-index__intro {
    clear: none;
  }
}
/*
* Media Queries
*/
@media (min-width: 768px) {
  .page-content-body--whats-hot .page-right-sidebar {
    margin-top: 0;
  }
  .page-content-body--whats-hot .page-right-sidebar h3 {
    margin-top: 0;
  }

  .whats-hot-intro {
    padding: 0 18px;
  }

  .whats-hot-index__hot {
    float: left;
    width: 31%;
    padding: 0 18px;
  }
  .whats-hot-index__hot.whats-hot-index__hot--read-more {
    display: block;
  }
  .whats-hot-index__hot:nth-of-type(3n+2) {
    margin-right: 20px;
    margin-left: 20px;
  }
  .whats-hot-index__hot:nth-of-type(3n+1) {
    clear: both;
  }

  .whats-hot-index__image {
    float: none;
    width: auto;
    margin: 0 -10px;
  }
  .whats-hot-index__image img {
    width: 100%;
  }

  .whats-hot-index__read-more {
    display: none;
  }

  .whats-hot-main-image {
    margin: 0 10px 20px 0;
  }
  .whats-hot-main-image > div {
    display: block;
    width: 38.2543103448%;
    float: right;
  }
  .whats-hot-main-image > div:first-child {
    width: 61.7456896552%;
    float: left;
  }

  .whats-hot-view {
    float: left;
    width: 100%;
    margin-right: -260px;
    padding: 0 18px;
  }

  .whats-hot-view__inner {
    margin-right: 230px;
  }

  .whats-hot-index .tile-panel {
    width: 30%;
    width: calc(33% - 12px);
  }

  .whats-hot-index .tile-panel:nth-child(even) {
    margin-right: 20px;
  }

  .whats-hot-index .tile-panel:nth-child(3n) {
    margin-right: 0;
  }
}
@media (min-width: 980px) {
  .whats-hot-index__hot {
    width: 230px;
  }

  .whats-hot-main-image {
    margin-right: 0;
  }
  .whats-hot-main-image > div {
    width: 355px;
    float: right;
  }
  .whats-hot-main-image > div:first-child {
    width: 573px;
    float: left;
  }
  .whats-hot-main-image .whats-hot-main-image__main-image {
    margin-right: 10px;
  }
  .whats-hot-main-image .whats-hot-main-image__main-image--wide {
    margin-right: 0;
  }
  .whats-hot-main-image .whats-hot-main-image__main-image--wide:first-child {
    width: 100%;
  }

  .whats-hot-view {
    width: calc(100% - 260px);
    padding: 0 0 0 10px;
  }

  .whats-hot-view__inner {
    margin-right: 0;
  }
}
@media (min-width: 1280px) {
  .whats-hot-intro {
    padding: 0;
  }

  .whats-hot-index .tile-panel {
    width: 22%;
    width: calc(25% - 15px);
  }

  .whats-hot-index .tile-panel:nth-child(3n) {
    margin-right: 20px;
  }

  .whats-hot-index .tile-panel:nth-child(4n) {
    margin-right: 0;
  }
}
/*------------------------------------*\
    $MISC
\*------------------------------------*/
*,
*:before,
*:after {
  box-sizing: border-box;
}

hr {
  border-top: 1px solid rgba(86, 80, 68, 0.4);
}



/*# sourceMappingURL=maps/screen.css.map */
