/* GENERAL */

.page-header__logo { background: url('/img/site/sprites/logos/bv-logo-white.svg') no-repeat center center; text-indent: 999px}

@media screen and (max-width: 767px) {
  .page-content-body .image-and-map__map-wrapper {
        display: none!important;
    }
}

/* TRIP CTAS */

@media screen and ( max-width: 450px ) {
    .itinerary-cta {flex-direction:column; padding: 30px!important;}
    .itinerary-cta-inner {flex-direction:column; margin-bottom: 20px; text-align: center; justify-content:center}
    .itinerary-cta-inner .card__icon { margin: 0 10px 10px 10px;}
    .itinerary-cta strong {display: block; margin: 0 0 5px 0!important}
}

@media screen and ( max-width: 700px ) {
    .itinerary-cta * {font-size: 18px; line-height: 24px;}
}

.itinerary-cta { width: 100%; padding: 20px; background-color: white; margin: 30px 0 20px 0; box-shadow: 0 0 2px rgba(97,97,97,.25); text-align: left; display:flex; justify-content: space-between; align-items: center;    font-size: 22px; line-height: 26px; color: #4b4742;font-weight: 300; position:relative}
.itinerary-cta-inner {display:flex;align-items:center}
.itinerary-cta strong {color: #36322B; margin-right:10px;}
.itinerary-cta a:hover {
    text-decoration: none!important;
}

.itinerary-cta-inner > img {
    margin: 0 15px 0 0;
    width:35px;
    height: 35px;

}
.itinerary-cta .button {
    font-size: 14px; line-height: 16px; color: white; text-transform: uppercase; border-radius: 0; background: rgb(168,0,0); padding: 12px 18px 14px 20px; display: flex; font-weight: bold; margin: 0 0 0 20px; align-items: center; white-space:nowrap}

.itinerary-cta .button img {
    width: 14px;
    height: 14px;
    margin-left: 5px;
    display:inline-block;
}

a.cover {
    position:absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 9999999999;
}




/* ACCORDION */
.section-content__inner { transition: max-height .1s ease-in-out!important;}


/* MOBILE BAR */

.mobile-bar {
    position:fixed; bottom: 0; left:0; right: 0;
    box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
    background: hsla(0, 0%, 93%, .9);
    transition:bottom .1s ease-in-out!important;
    z-index: 9999999999;
    max-width: 100vw;
  }
  .mobile-bar a[disabled] { opacity: 0.5; pointer-events: none;}
  .mobile-bar-show { display:block }
  .mobile-bar-hide { bottom:-100px }


/* MOBILE FOOTER */

@media screen and (max-width: 767px) {

    .nav-medium-up { display: none ;}

    .nav-small ul {
        display: flex;
        list-style-type: none;
        justify-content: space-around;
        padding-left: 0;
    }
    .nav-small ul li a {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #A80000;
        transition: color 0.25s;
    }
    .nav-small ul li a:hover {
        text-decoration: none!important;
        color: #e00808
    }

    .nav-small li.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
    .nav-small img {
        width: 32px; height: 32px;

    }
    .nav-small span {
        font-size: 12px; color: #474747;
        line-height: 14px;
        margin-top: 7px;
    }

    [data-id="zsalesiq"] {
        display: none!important;
    }
}

/* DESKTOP FOOTER */

@media screen and (min-width: 768px) {


.arrow-orange-down-14:after,.arrow-orange-right-14:after,.arrow-right-14:after,.arrow-right-red-14:after,.contact-form button:after,.content-block__link a:after,.enews-module__form button:after,.faqs-index__questions li a:after,.feefo__all:after,.gallery-module__read-more a:after,.home-columns .home-column__more-link a:after,.pagination__pages li:last-child a:after,.press-release__text__link a:after,.quote-module__request:after,.related-module__link a:after,.related-module__view-all a:after,.testimonial-extract__read-more a:after,.ui-modal-close:after,.whats-hot-index__hot.whats-hot-index__hot--read-more a:after,.whats-hot-index__read-more a:after,.youtube-videos__link a:after {
    top: 0;
  }
  .page-wrapper__contact-module .call-us-module {
    padding-top: 0;
  }

  .page-wrapper__contact-module .call-us-module p {
    margin: 0 10px;
    font-size: 20px;
  }
  .page-wrapper__contact-module .call-us-module p:last-child {
    margin-bottom:0;
  }
  .page-wrapper__contact-module .call-us-module .nav-bar{
    padding: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .page-wrapper__contact-module .call-us-module p:first-child {
    margin-top: 0;
  }

  .page-wrapper__contact-module .call-us-module .nav-small { display: none ;}

}


/* Override background image */
.destinations-nav {
  background-image: none;
  padding-left:
}

@media screen and (max-width: 979px) {
  .destinations-nav {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.destinations-nav ul {
  display: flex;
  flex-grow: 1; /* Make ul expand to full width of its container */
  list-style-type: none;
  margin: 0 0 15px 0;
  padding: 0;
  position: relative; /* Ensure the pseudo-element positions relative to its parent */
  border-top: none;
  border-bottom: none
}

.destinations-nav button, .destinations-nav li:first-child button {
height: 60px;
border: 2px solid #ccc;
color: #bbb;
}
.destinations-nav .active button {
  height: 60px;
  border-left: 2px solid #ccc;
  color: #333!important;
  border-bottom-width: 0!important;
}

.destinations-nav li:not(:last-child) button {
  border-right-width: 0!important;
}

.destinations-nav ul::after {
  content: '';
  flex-grow: 1;
  border-bottom: 2px solid #ccc; /* The line color */
  align-self: flex-end; /* Align line to bottom of flex container */
}

@media (min-width: 980px) {
  .page-content-body .image-and-map {
      width: 100%;
      float:none;
  }
}

.travel-plan__header {
    margin-bottom: 0;
}
.js-travel-plan-form {
    margin-top: var(--spacing-80px);
}

.nowrap {
    white-space: nowrap;
}

.checkbox-spaced input[type="checkbox"] {
    margin-right: 5px;
}

.checkbox-spaced .checkbox {
    display: inline-block;
    margin-right: 15px;
}

.margin-bottom {
    margin-bottom: 15px;
}

.feefo-link {
    margin-top: 10px;
    margin-bottom: 20px;
}

.feefo-card img[alt="Feefo Reviews"] {
    margin-top: 10px;
}

.opening-hours__row {
    padding: 0.25rem 0;
}

.card__image {
    max-height: 100%;
}

.ready-to-plan-mobile {
    display: block;
    margin-bottom: 20px;
    margin-top: -30px;
}

.ready-to-plan-desktop {
    display: none;
}

@media screen and (min-width: 1280px) {
    .ready-to-plan-mobile {
        display: none;
    }

    .ready-to-plan-desktop {
        display: block;
    }
}
.thirdp-associations__discover-america {
    background: none;
    text-indent: 0!important;
}
.thirdp-associations__discover-america img {
    width: 110px;
    height: 61px;
    display: inline-block;
    vertical-align: middle;
}

#mobile-menu:not(.mm-menu) ul {
  display:none;
}

.mm-menu a:focus-visible { outline: none; }

/* MAP FIXES */

/* Reset the default image-and-map styles for page-content-body context to prevent white bars */
.page-content-body .image-and-map {
  padding: 0 !important;
  background-color: transparent !important;
  box-shadow: none !important;
  line-height: normal !important;
}

/* Restore padding and styling only for full-width maps (itinerary pages) */
.page-content-body .image-and-map--itinerary .image-and-map__map-wrapper--full-width {
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 0 2px rgba(97, 97, 97, 0.25);
  line-height: 0;
}

/* Restore padding and styling for images on destination pages */
.page-content-body .image-and-map--itinerary .image-and-map__image.image-and-map__image--full-image {
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 0 2px rgba(97, 97, 97, 0.25);
  line-height: 0;
}

/* Fix z-index issue for fixed maps */
.image-and-map.image-and-map--itinerary {
  position: relative !important;
  z-index: 999 !important;
}
